# Production Environment Variables
# Copy this file to .env and update with your production values

# MongoDB Configuration
MONGO_ROOT_PASSWORD=your-secure-mongo-password
MONGODB_URI=********************************************************************************

# Cloudinary Configuration (Required)
CLOUDINARY_CLOUD_NAME=your-cloudinary-cloud-name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# Server Configuration
NODE_ENV=production
PORT=5000
JWT_SECRET=your-super-secure-jwt-secret-key-here
ADMIN_PASSWORD=your-secure-admin-password

# Frontend Configuration
REACT_APP_API_URL=https://your-domain.com
REACT_APP_SOCKET_URL=https://your-domain.com

# Grid Configuration
DEFAULT_GRID_ROWS=10
DEFAULT_GRID_COLS=10
MAX_GRID_SIZE=50

# SSL Configuration (for production with HTTPS)
SSL_CERT_PATH=/path/to/ssl/cert.pem
SSL_KEY_PATH=/path/to/ssl/key.pem

# Backup Configuration
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30

# Monitoring
LOG_LEVEL=info
ENABLE_METRICS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Limits
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES=image/jpeg,image/jpg,image/png,image/gif,image/webp
