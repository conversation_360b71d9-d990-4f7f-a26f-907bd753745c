/* Admin Container */
.admin-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  color: white;
  overflow-y: auto;
  max-height: 100vh;
}

/* Admin Header */
.admin-header {
  text-align: center;
  margin-bottom: 30px;
}

.admin-header h1 {
  font-size: 2.5rem;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.admin-stats {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.stat-item {
  background: rgba(255, 255, 255, 0.1);
  padding: 10px 20px;
  border-radius: 25px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  font-weight: 500;
}

/* Admin Loading */
.admin-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 50vh;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Admin Error */
.admin-error {
  background: rgba(255, 0, 0, 0.2);
  border: 1px solid rgba(255, 0, 0, 0.5);
  padding: 15px;
  border-radius: 10px;
  margin-bottom: 20px;
  text-align: center;
  backdrop-filter: blur(10px);
}

/* Admin Actions */
.admin-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

/* Buttons */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-danger {
  background: linear-gradient(45deg, #ff6b6b, #ee5a52);
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: linear-gradient(45deg, #ff5252, #e53935);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.btn-success {
  background: linear-gradient(45deg, #4caf50, #45a049);
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: linear-gradient(45deg, #45a049, #3d8b40);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.btn-sm {
  padding: 8px 16px;
  font-size: 0.9rem;
  min-width: 80px;
}

/* Photos Grid */
.admin-photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Photo Card */
.admin-photo-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.admin-photo-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.admin-photo-card.pending {
  border-color: #ffc107;
  box-shadow: 0 0 20px rgba(255, 193, 7, 0.3);
}

/* Photo Preview */
.photo-preview {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.photo-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.admin-photo-card:hover .photo-preview img {
  transform: scale(1.05);
}

.pending-badge {
  position: absolute;
  top: 10px;
  right: 10px;
  background: #ffc107;
  color: #000;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Photo Info */
.photo-info {
  padding: 15px;
}

.guest-name {
  font-weight: 600;
  font-size: 1.1rem;
  margin-bottom: 5px;
}

.upload-date, .grid-position {
  font-size: 0.9rem;
  opacity: 0.8;
  margin-bottom: 3px;
}

/* Photo Actions */
.photo-actions {
  padding: 15px;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Empty State */
.admin-empty {
  text-align: center;
  padding: 60px 20px;
  opacity: 0.7;
}

.admin-empty p {
  font-size: 1.2rem;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  padding: 30px;
  border-radius: 15px;
  max-width: 500px;
  width: 100%;
  text-align: center;
  backdrop-filter: blur(10px);
}

.modal-content h3 {
  margin-bottom: 15px;
  color: #333;
}

.modal-content p {
  margin-bottom: 25px;
  line-height: 1.6;
  color: #666;
}

.modal-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-container {
    padding: 15px;
  }
  
  .admin-header h1 {
    font-size: 2rem;
  }
  
  .admin-stats {
    gap: 15px;
  }
  
  .stat-item {
    padding: 8px 15px;
    font-size: 0.9rem;
  }
  
  .admin-photos-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .admin-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn {
    width: 100%;
    max-width: 200px;
  }
  
  .modal-actions {
    flex-direction: column;
  }
  
  .modal-actions .btn {
    width: 100%;
  }
}
