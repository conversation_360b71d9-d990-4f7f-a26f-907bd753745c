import { useState, useEffect } from 'react';

const BaseImageOverlay = ({
  baseImageUrl,
  gridRows,
  gridCols,
  tileSize,
  screenDimensions
}) => {
  const [baseImageLoaded, setBaseImageLoaded] = useState(false);

  // Load base image
  useEffect(() => {
    if (!baseImageUrl) {
      setBaseImageLoaded(false);
      return;
    }

    const img = new Image();
    img.onload = () => {
      setBaseImageLoaded(true);
    };
    img.onerror = () => {
      console.error('Failed to load base image:', baseImageUrl);
      setBaseImageLoaded(false);
    };
    img.src = baseImageUrl;
  }, [baseImageUrl]);

  if (!baseImageUrl || !baseImageLoaded || !gridRows || !gridCols) {
    return null;
  }

  // Handle both square (number) and rectangular (object) tile sizes
  const tileWidth = typeof tileSize === 'object' ? tileSize.width : tileSize;
  const tileHeight = typeof tileSize === 'object' ? tileSize.height : tileSize;

  // Use full screen dimensions for the base image overlay
  const totalGridWidth = screenDimensions ? screenDimensions.width : gridCols * tileWidth;
  const totalGridHeight = screenDimensions ? screenDimensions.height : gridRows * tileHeight;

  return (
    <div
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: totalGridWidth,
        height: totalGridHeight,
        pointerEvents: 'none',
        zIndex: 2,
        display: 'grid',
        gridTemplateColumns: `repeat(${gridCols}, ${tileWidth}px)`,
        gridTemplateRows: `repeat(${gridRows}, ${tileHeight}px)`,
        gap: 0
      }}
    >
      {Array.from({ length: gridRows * gridCols }, (_, index) => {
        const row = Math.floor(index / gridCols);
        const col = index % gridCols;

        return (
          <div
            key={`${row}-${col}`}
            style={{
              width: tileWidth,
              height: tileHeight,
              overflow: 'hidden',
              position: 'relative',
              opacity: 0.8
            }}
          >
            <img
              src={baseImageUrl}
              alt={`Base piece ${row}-${col}`}
              style={{
                width: totalGridWidth,
                height: totalGridHeight,
                position: 'absolute',
                left: -(col * tileWidth),
                top: -(row * tileHeight),
                objectFit: 'cover',
                imageRendering: 'high-quality',
                filter: 'brightness(1.1) contrast(0.9)' // Enhance visibility with photos
              }}
            />
          </div>
        );
      })}
    </div>
  );
};

export default BaseImageOverlay;
