const express = require('express');
const router = express.Router();
const { Event, Photo } = require('../models');
const mosaicService = require('../services/mosaicService');
const { upload } = require('../config/cloudinary');
const { processBaseImageToGrid } = require('../utils/colorExtraction');

// GET /api/mosaic/analysis/:eventId - Get mosaic analysis
router.get('/analysis/:eventId?', async (req, res) => {
  try {
    let eventId = req.params.eventId;
    
    if (!eventId || eventId === 'default') {
      const event = await Event.getDefault();
      eventId = event._id;
    }

    const analysis = await mosaicService.getMosaicAnalysis(eventId);
    
    res.json(analysis);
  } catch (error) {
    console.error('Error getting mosaic analysis:', error);
    res.status(500).json({ 
      error: 'Failed to get mosaic analysis',
      message: error.message 
    });
  }
});

// POST /api/mosaic/arrange/:eventId - Arrange photos in grid based on color matching
router.post('/arrange/:eventId?', async (req, res) => {
  try {
    let eventId = req.params.eventId;
    
    if (!eventId || eventId === 'default') {
      const event = await Event.getDefault();
      eventId = event._id;
    }

    const { targetPalette } = req.body;
    
    const result = await mosaicService.arrangePhotosInGrid(eventId, targetPalette);
    
    // Emit socket event for real-time updates
    const socketHandler = req.app.get('socketHandler');
    if (socketHandler) {
      socketHandler.broadcastAdminAction(eventId, 'photos-rearranged', {
        totalArranged: result.totalArranged,
        totalPhotos: result.totalPhotos
      });
    }

    res.json(result);
  } catch (error) {
    console.error('Error arranging photos:', error);
    res.status(500).json({ 
      error: 'Failed to arrange photos',
      message: error.message 
    });
  }
});

// POST /api/mosaic/optimize/:eventId - Optimize photo placement
router.post('/optimize/:eventId?', async (req, res) => {
  try {
    let eventId = req.params.eventId;
    
    if (!eventId || eventId === 'default') {
      const event = await Event.getDefault();
      eventId = event._id;
    }

    const { iterations = 100 } = req.body;
    
    const result = await mosaicService.optimizePhotoPlacement(eventId, iterations);
    
    // Emit socket event for real-time updates
    const socketHandler = req.app.get('socketHandler');
    if (socketHandler) {
      socketHandler.broadcastAdminAction(eventId, 'photos-optimized', {
        optimizationScore: result.optimizationScore,
        iterations: result.iterations,
        totalPhotos: result.totalPhotos
      });
    }

    res.json(result);
  } catch (error) {
    console.error('Error optimizing photos:', error);
    res.status(500).json({ 
      error: 'Failed to optimize photos',
      message: error.message 
    });
  }
});

// POST /api/mosaic/generate-palette - Generate target color palette
router.post('/generate-palette', async (req, res) => {
  try {
    const { imageUrl, gridRows = 10, gridCols = 10 } = req.body;
    
    if (gridRows < 1 || gridRows > 50 || gridCols < 1 || gridCols > 50) {
      return res.status(400).json({ 
        error: 'Grid size must be between 1x1 and 50x50' 
      });
    }

    const palette = await mosaicService.generateTargetPalette(imageUrl, gridRows, gridCols);
    
    res.json({
      success: true,
      palette,
      gridSize: {
        rows: gridRows,
        cols: gridCols
      }
    });
  } catch (error) {
    console.error('Error generating palette:', error);
    res.status(500).json({ 
      error: 'Failed to generate palette',
      message: error.message 
    });
  }
});

// GET /api/mosaic/color-stats/:eventId - Get color statistics for event
router.get('/color-stats/:eventId?', async (req, res) => {
  try {
    let eventId = req.params.eventId;
    
    if (!eventId || eventId === 'default') {
      const event = await Event.getDefault();
      eventId = event._id;
    }

    const analysis = await mosaicService.getMosaicAnalysis(eventId);
    
    res.json({
      success: true,
      colorStats: analysis.colorStats,
      eventInfo: analysis.eventInfo
    });
  } catch (error) {
    console.error('Error getting color stats:', error);
    res.status(500).json({ 
      error: 'Failed to get color statistics',
      message: error.message 
    });
  }
});

// POST /api/mosaic/shuffle/:eventId - Randomly shuffle photo positions
router.post('/shuffle/:eventId?', async (req, res) => {
  try {
    let eventId = req.params.eventId;
    
    if (!eventId || eventId === 'default') {
      const event = await Event.getDefault();
      eventId = event._id;
    }

    const { Photo } = require('../models');
    
    // Get all photos for the event
    const photos = await Photo.find({
      eventId,
      isVisible: true,
      isApproved: true
    });

    if (photos.length === 0) {
      return res.json({
        success: true,
        message: 'No photos to shuffle'
      });
    }

    // Get event for grid size
    const event = await Event.findById(eventId);
    const totalSlots = event.gridRows * event.gridCols;
    
    // Create array of available positions
    const positions = [];
    for (let row = 0; row < event.gridRows; row++) {
      for (let col = 0; col < event.gridCols; col++) {
        positions.push({ row, col });
      }
    }

    // Shuffle positions
    for (let i = positions.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [positions[i], positions[j]] = [positions[j], positions[i]];
    }

    // Assign shuffled positions to photos
    const updates = [];
    for (let i = 0; i < Math.min(photos.length, positions.length); i++) {
      const photo = photos[i];
      const position = positions[i];
      
      await Photo.findByIdAndUpdate(photo._id, {
        gridRow: position.row,
        gridCol: position.col
      });
      
      updates.push({
        photoId: photo._id,
        oldPosition: { row: photo.gridRow, col: photo.gridCol },
        newPosition: position
      });
    }

    // Update event statistics
    await event.updateStats();

    // Emit socket event for real-time updates
    const socketHandler = req.app.get('socketHandler');
    if (socketHandler) {
      socketHandler.broadcastAdminAction(eventId, 'photos-shuffled', {
        totalShuffled: updates.length
      });
    }

    res.json({
      success: true,
      message: 'Photos shuffled successfully',
      totalShuffled: updates.length,
      updates
    });
  } catch (error) {
    console.error('Error shuffling photos:', error);
    res.status(500).json({ 
      error: 'Failed to shuffle photos',
      message: error.message 
    });
  }
});

// POST /api/mosaic/base-image - Upload base image for mosaic
router.post('/base-image', upload.single('baseImage'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No base image uploaded' });
    }

    const event = await Event.getDefault();

    // Update event with base image info
    event.baseImage = {
      cloudinaryUrl: req.file.path,
      cloudinaryPublicId: req.file.public_id,
      description: req.body.description || 'Base image for mosaic',
      uploadedAt: new Date()
    };

    await event.save();

    res.json({
      success: true,
      message: 'Base image uploaded successfully',
      baseImage: event.baseImage
    });
  } catch (error) {
    console.error('Error uploading base image:', error);
    res.status(500).json({
      error: 'Failed to upload base image',
      message: error.message
    });
  }
});

// POST /api/mosaic/process-base-image - Process base image into grid colors
router.post('/process-base-image', async (req, res) => {
  try {
    console.log('🎨 Starting base image processing...');

    const event = await Event.getDefault();
    console.log('📋 Event loaded:', {
      id: event._id,
      gridSize: `${event.gridRows}x${event.gridCols}`,
      hasBaseImage: !!event.baseImage?.cloudinaryUrl
    });

    if (!event.baseImage?.cloudinaryUrl) {
      return res.status(400).json({ error: 'No base image found. Please upload a base image first.' });
    }

    console.log('🖼️ Processing base image:', event.baseImage.cloudinaryUrl);

    // Process base image using utility function
    const colorData = await processBaseImageToGrid(
      event.baseImage.cloudinaryUrl,
      event.gridRows,
      event.gridCols
    );

    console.log(`🎨 Extracted ${colorData.length} color samples`);

    // Initialize grid map and update with colors
    console.log('📊 Initializing grid map...');
    await event.initializeGridMap();

    console.log('🎨 Updating grid map with colors...');
    await event.updateGridMapColors(colorData);

    console.log(`✅ Successfully processed ${colorData.length} grid cells for mosaic`);

    // Emit socket event for real-time updates
    const socketHandler = req.app.get('socketHandler');
    if (socketHandler) {
      socketHandler.broadcastAdminAction(event._id, 'base-image-processed', {
        totalCells: colorData.length,
        gridSize: { rows: event.gridRows, cols: event.gridCols }
      });
    }

    res.json({
      success: true,
      message: 'Base image processed successfully',
      gridMap: event.gridMap,
      totalCells: colorData.length,
      gridSize: { rows: event.gridRows, cols: event.gridCols }
    });

  } catch (error) {
    console.error('❌ Error processing base image:', error);
    console.error('Stack trace:', error.stack);

    res.status(500).json({
      error: 'Failed to process base image',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    });
  }
});

// PUT /api/mosaic/settings - Update mosaic settings
router.put('/settings', async (req, res) => {
  try {
    const { enableColorMatching, mosaicMode } = req.body;
    const event = await Event.getDefault();

    if (enableColorMatching !== undefined) {
      event.enableColorMatching = enableColorMatching;
    }

    if (mosaicMode && ['color-match', 'simple'].includes(mosaicMode)) {
      event.mosaicMode = mosaicMode;
    }

    await event.save();

    // Emit socket event for real-time updates
    const socketHandler = req.app.get('socketHandler');
    if (socketHandler) {
      socketHandler.broadcastAdminAction(event._id, 'mosaic-settings-updated', {
        enableColorMatching: event.enableColorMatching,
        mosaicMode: event.mosaicMode
      });
    }

    res.json({
      success: true,
      message: 'Mosaic settings updated',
      settings: {
        enableColorMatching: event.enableColorMatching,
        mosaicMode: event.mosaicMode
      }
    });

  } catch (error) {
    console.error('Error updating mosaic settings:', error);
    res.status(500).json({
      error: 'Failed to update mosaic settings',
      message: error.message
    });
  }
});

// GET /api/mosaic/status - Get mosaic status and statistics
router.get('/status', async (req, res) => {
  try {
    const event = await Event.getDefault();

    const totalCells = event.gridRows * event.gridCols;
    const occupiedCells = event.gridMap ? event.gridMap.filter(cell => cell.occupied).length : 0;
    const fillPercentage = totalCells > 0 ? Math.round((occupiedCells / totalCells) * 100) : 0;

    res.json({
      success: true,
      status: {
        hasBaseImage: !!event.baseImage?.cloudinaryUrl,
        enableColorMatching: event.enableColorMatching,
        mosaicMode: event.mosaicMode,
        gridSize: {
          rows: event.gridRows,
          cols: event.gridCols,
          total: totalCells
        },
        fillStatus: {
          occupied: occupiedCells,
          available: totalCells - occupiedCells,
          percentage: fillPercentage
        },
        baseImage: event.baseImage
      }
    });

  } catch (error) {
    console.error('Error getting mosaic status:', error);
    res.status(500).json({
      error: 'Failed to get mosaic status',
      message: error.message
    });
  }
});

module.exports = router;
