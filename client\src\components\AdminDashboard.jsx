import { useState, useEffect } from 'react';
// Removed framer-motion for better performance

import { useSocket } from '../context/SocketContext';
import { photoAPI, gridAPI, handleAPIError } from '../services/api';

const AdminDashboard = () => {
  const { gridState, connected } = useSocket();
  const [photos, setPhotos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [photosPerPage] = useState(12); // Show 12 photos per page
  const [totalPhotos, setTotalPhotos] = useState(0);
  const [paginationLoading, setPaginationLoading] = useState(false);

  const [gridConfig, setGridConfig] = useState({ rows: 10, cols: 10 });
  const [showGridConfig, setShowGridConfig] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [mosaicOperating, setMosaicOperating] = useState(false);

  // Base image state
  const [baseImageData, setBaseImageData] = useState(null);
  const [uploadingBaseImage, setUploadingBaseImage] = useState(false);





  useEffect(() => {
    // Add admin page class to body for scrolling
    document.body.classList.add('admin-page');

    loadPhotos();
    loadGridConfig();
    loadBaseImage();

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('admin-page');
    };
  }, []);

  // Listen for real-time grid updates to refresh admin counters
  useEffect(() => {
    if (!gridState) return;

    // Update local grid config when gridState changes
    if (gridState.gridSize) {
      setGridConfig({
        rows: gridState.gridSize.rows,
        cols: gridState.gridSize.cols
      });
    }

    console.log('📊 Admin: Grid state updated, refreshing stats');
  }, [gridState]);

  useEffect(() => {
    if (gridState?.gridSize) {
      setGridConfig({
        rows: gridState.gridSize.rows,
        cols: gridState.gridSize.cols
      });
    }
  }, [gridState]);

  const loadPhotos = async (page = currentPage) => {
    try {
      setLoading(true);
      const result = await photoAPI.getPhotos(); // Get all photos without filter
      const allPhotos = result.photos || [];

      // Set total count first
      setTotalPhotos(allPhotos.length);

      // If no photos, reset pagination
      if (allPhotos.length === 0) {
        setPhotos([]);
        setCurrentPage(1);
        return;
      }

      // Calculate pagination
      const totalPages = Math.ceil(allPhotos.length / photosPerPage);
      const validPage = Math.min(Math.max(1, page), totalPages);

      const startIndex = (validPage - 1) * photosPerPage;
      const endIndex = startIndex + photosPerPage;
      const paginatedPhotos = allPhotos.slice(startIndex, endIndex);

      setPhotos(paginatedPhotos);
      setCurrentPage(validPage);


    } catch (error) {
      const errorInfo = handleAPIError(error);
      setError(errorInfo.message);
    } finally {
      setLoading(false);
    }
  };



  const loadGridConfig = async () => {
    try {
      const result = await gridAPI.getGridConfig();
      setGridConfig({
        rows: result.gridConfig.rows,
        cols: result.gridConfig.cols
      });
    } catch (error) {
      console.error('Failed to load grid config:', error);
    }
  };

  const updateGridConfig = async () => {
    if (gridConfig.rows < 1 || gridConfig.rows > 50 ||
        gridConfig.cols < 1 || gridConfig.cols > 50) {
      setError('Grid size must be between 1x1 and 50x50');
      return;
    }

    try {
      setUpdating(true);
      setError('');

      await gridAPI.updateGridConfig({
        rows: parseInt(gridConfig.rows),
        cols: parseInt(gridConfig.cols)
      });

      setShowGridConfig(false);

      // Reload all data to update counters
      loadPhotos();
      loadStats();
      loadGridConfig(); // Reload grid config to sync


      console.log('✅ Grid configuration updated successfully');
    } catch (error) {
      const errorInfo = handleAPIError(error);
      setError(errorInfo.message);
    } finally {
      setUpdating(false);
    }
  };

  // Load base image data
  const loadBaseImage = async () => {
    try {
      const response = await gridAPI.getStatus();
      if (response.success && response.status.baseImage) {
        setBaseImageData(response.status.baseImage);
      }
    } catch (error) {
      console.error('Failed to load base image:', error);
    }
  };

  // Handle base image upload
  const handleBaseImageUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      setUploadingBaseImage(true);
      setError('');

      const formData = new FormData();
      formData.append('baseImage', file);

      const response = await gridAPI.uploadBaseImage(formData);

      if (response.success) {
        setBaseImageData(response.baseImage);
        console.log('✅ Base image uploaded successfully');

        // Refresh photos to update wall
        await loadPhotos();
      }
    } catch (error) {
      console.error('❌ Base image upload failed:', error);
      const errorInfo = handleAPIError(error);
      setError(`Base image upload failed: ${errorInfo.message}`);
    } finally {
      setUploadingBaseImage(false);
      // Clear the input
      event.target.value = '';
    }
  };

  // Pagination functions
  const goToPage = async (page) => {
    if (page >= 1 && page <= Math.ceil(totalPhotos / photosPerPage) && !paginationLoading) {
      setPaginationLoading(true);
      await loadPhotos(page);
      setPaginationLoading(false);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1 && !paginationLoading) {
      goToPage(currentPage - 1);
    }
  };

  const goToNextPage = () => {
    if (currentPage < Math.ceil(totalPhotos / photosPerPage) && !paginationLoading) {
      goToPage(currentPage + 1);
    }
  };





  const handleDeletePhoto = async (photoId) => {
    if (!confirm('Are you sure you want to delete this photo?')) {
      return;
    }

    try {
      await photoAPI.deletePhoto(photoId);
      setPhotos(photos.filter(photo => photo._id !== photoId));
    } catch (error) {
      const errorInfo = handleAPIError(error);
      setError(errorInfo.message);
    }
  };

  const handleDownloadAll = async () => {
    try {
      setError('');

      const response = await fetch('/api/photos/download', {
        method: 'GET',
      });

      if (!response.ok) {
        throw new Error('Failed to download photos');
      }

      // Create download link
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `mosaic-photos-${new Date().toISOString().split('T')[0]}.zip`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      alert('Photos downloaded successfully!');
    } catch (error) {
      const errorInfo = handleAPIError(error);
      setError(errorInfo.message);
    }
  };

  const handleDeleteAllPhotos = async () => {
    if (!confirm(`Are you sure you want to delete ALL ${photos.length} photos? This action cannot be undone and will remove photos from both the database and Cloudinary storage.`)) {
      return;
    }

    try {
      setMosaicOperating(true);
      setError('');

      const response = await fetch('/api/photos/all', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete all photos');
      }

      const result = await response.json();

      // Refresh photos list and stats
      await loadPhotos();



      alert(`Successfully deleted ${result.deletedCount} photos`);
    } catch (error) {
      console.error('Error deleting all photos:', error);
      setError('Failed to delete all photos: ' + error.message);
    } finally {
      setMosaicOperating(false);
    }
  };

  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    try {
      setMosaicOperating(true);
      setError('');

      let successCount = 0;
      let failedFiles = [];

      for (const file of files) {
        try {
          // Use original file - NO compression
          console.log('📸 Using original file for admin upload');

          // Use photoAPI for consistent error handling
          const formData = new FormData();
          formData.append('photo', file);
          formData.append('guestName', 'Admin Upload');

          const result = await photoAPI.uploadPhoto(formData);

          if (result.success) {
            successCount++;
            console.log(`✅ Uploaded: ${file.name}`);
          } else {
            failedFiles.push(file.name);
          }
        } catch (fileError) {
          console.error(`❌ Failed to upload ${file.name}:`, fileError);
          failedFiles.push(file.name);

          // Continue with other files instead of stopping
          continue;
        }
      }

      // Refresh data after all uploads
      await loadPhotos();

      // Show results
      if (successCount > 0) {
        const message = `Successfully uploaded ${successCount} photo(s)`;
        console.log(`✅ ${message}`);
        if (failedFiles.length === 0) {
          setError(''); // Clear any previous errors
        }
      }

      if (failedFiles.length > 0) {
        const errorMsg = `Failed to upload ${failedFiles.length} file(s): ${failedFiles.join(', ')}`;
        setError(errorMsg);
      }

      // Clear the input
      event.target.value = '';
    } catch (error) {
      console.error('Error in upload process:', error);
      setError('Upload process failed: ' + error.message);
    } finally {
      setMosaicOperating(false);
    }
  };

  if (loading) {
    return (
      <div className="page-container">
        <div className="loading-overlay">
          <div className="loading-content">
            <div className="loading-spinner"></div>
            <p>Loading admin dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="page-container" style={{ padding: '2rem', background: '#111' }}>
      <header style={{ marginBottom: '2rem' }}>
        <h1 style={{ color: '#fff', marginBottom: '1rem' }}>Admin Dashboard</h1>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', marginBottom: '1rem' }}>
          <span style={{ color: connected ? '#28a745' : '#dc3545' }}>
            {connected ? '🟢 Connected' : '🔴 Disconnected'}
          </span>

        </div>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <button
              onClick={() => setShowGridConfig(!showGridConfig)}
              style={{
                padding: '0.5rem 1rem',
                background: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Configure Grid
            </button>
            <button
              onClick={() => setShowGridConfig(!showGridConfig)}
              style={{
                padding: '0.5rem 1rem',
                background: '#ff6b35',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              🎨 Mosaic Setup
            </button>

            <button
              onClick={() => window.location.href = '/'}
              style={{
                padding: '0.5rem 1rem',
                background: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              View Mosaic
            </button>
          </div>
        </div>
      </header>

      {error && (
        <div className="error-message" style={{ marginBottom: '2rem' }}>
          {error}
        </div>
      )}

      {/* Grid Configuration Modal */}
      {showGridConfig && (
        <div
          style={{
            background: '#222',
            padding: '2rem',
            borderRadius: '8px',
            marginBottom: '2rem',
            border: '1px solid #333'
          }}
        >
          <h3 style={{ color: '#fff', marginBottom: '1rem' }}>🎨 Mosaic Setup</h3>
          <div style={{ display: 'flex', gap: '1rem', alignItems: 'end', marginBottom: '1rem' }}>
            <div>
              <label style={{ color: '#ccc', display: 'block', marginBottom: '0.5rem' }}>
                Rows (1-50)
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={gridConfig.rows}
                onChange={(e) => setGridConfig(prev => ({ ...prev, rows: e.target.value }))}
                style={{
                  padding: '0.5rem',
                  border: '1px solid #555',
                  borderRadius: '4px',
                  background: '#333',
                  color: '#fff',
                  width: '80px'
                }}
                disabled={updating}
              />
            </div>
            <div>
              <label style={{ color: '#ccc', display: 'block', marginBottom: '0.5rem' }}>
                Columns (1-50)
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={gridConfig.cols}
                onChange={(e) => setGridConfig(prev => ({ ...prev, cols: e.target.value }))}
                style={{
                  padding: '0.5rem',
                  border: '1px solid #555',
                  borderRadius: '4px',
                  background: '#333',
                  color: '#fff',
                  width: '80px'
                }}
                disabled={updating}
              />
            </div>
            <div style={{ display: 'flex', gap: '0.5rem' }}>
              <button
                onClick={updateGridConfig}
                disabled={updating}
                style={{
                  padding: '0.5rem 1rem',
                  background: updating ? '#666' : '#28a745',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: updating ? 'not-allowed' : 'pointer'
                }}
              >
                {updating ? 'Updating...' : 'Update'}
              </button>
              <button
                onClick={() => setShowGridConfig(false)}
                disabled={updating}
                style={{
                  padding: '0.5rem 1rem',
                  background: '#666',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Cancel
              </button>
            </div>
          </div>
          <p style={{ color: '#ccc', fontSize: '0.9rem', margin: '0 0 2rem 0' }}>
            Current: {gridState?.gridSize?.rows || 'Loading...'}×{gridState?.gridSize?.cols || 'Loading...'}
            ({gridState?.stats?.totalPhotos || 0} photos, {gridState?.stats?.fillPercentage || 0}% filled)
          </p>

          {/* Base Image Upload Section */}
          <div style={{
            borderTop: '1px solid #444',
            paddingTop: '1.5rem',
            marginTop: '1.5rem'
          }}>
            <h4 style={{ color: '#fff', marginBottom: '1rem' }}>🖼️ Base Image</h4>
            <p style={{ color: '#ccc', fontSize: '0.9rem', marginBottom: '1rem' }}>
              Upload a base image that will be displayed as an overlay on the mosaic wall.
            </p>

            <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', marginBottom: '1rem' }}>
              <input
                type="file"
                accept="image/*"
                onChange={handleBaseImageUpload}
                disabled={uploadingBaseImage}
                style={{
                  padding: '0.5rem',
                  background: '#333',
                  color: '#fff',
                  border: '1px solid #555',
                  borderRadius: '4px'
                }}
              />
              {uploadingBaseImage && (
                <span style={{ color: '#ff6b35' }}>Uploading...</span>
              )}
            </div>

            {baseImageData && (
              <div style={{ marginTop: '1rem' }}>
                <p style={{ color: '#28a745', marginBottom: '0.5rem' }}>✅ Base image uploaded</p>
                <img
                  src={baseImageData.cloudinaryUrl}
                  alt="Base image"
                  style={{
                    maxWidth: '200px',
                    maxHeight: '150px',
                    borderRadius: '4px',
                    border: '1px solid #555'
                  }}
                />
              </div>
            )}
          </div>
        </div>
      )}







      {/* Stats Section */}
      {gridState && (
        <section style={{ marginBottom: '2rem' }}>
          <h2 style={{ color: '#fff', marginBottom: '1rem' }}>Statistics</h2>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '1rem' 
          }}>
            <div style={{ 
              background: '#222', 
              padding: '1rem', 
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <h3 style={{ color: '#007bff', margin: '0 0 0.5rem 0' }}>
                {gridState.stats?.totalPhotos || 0}
              </h3>
              <p style={{ color: '#ccc', margin: 0 }}>Total Photos</p>
            </div>
            <div style={{ 
              background: '#222', 
              padding: '1rem', 
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <h3 style={{ color: '#28a745', margin: '0 0 0.5rem 0' }}>
                {gridState.stats?.fillPercentage || 0}%
              </h3>
              <p style={{ color: '#ccc', margin: 0 }}>Grid Filled</p>
            </div>
            <div style={{ 
              background: '#222', 
              padding: '1rem', 
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <h3 style={{ color: '#ffc107', margin: '0 0 0.5rem 0' }}>
                {gridState.gridSize?.rows}×{gridState.gridSize?.cols}
              </h3>
              <p style={{ color: '#ccc', margin: 0 }}>Grid Size</p>
            </div>
          </div>
        </section>
      )}

      {/* Photos Management */}
      {(
        <section>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1rem',
          flexWrap: 'wrap',
          gap: '1rem'
        }}>
          <h2 style={{ color: '#fff', margin: 0 }}>Photos ({photos.length})</h2>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <button
              onClick={handleDownloadAll}
              disabled={photos.length === 0}
              style={{
                padding: '0.5rem 1rem',
                background: photos.length === 0 ? '#666' : '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: photos.length === 0 ? 'not-allowed' : 'pointer'
              }}
            >
              📥 Download All
            </button>
            <button
              onClick={handleDeleteAllPhotos}
              disabled={photos.length === 0}
              style={{
                padding: '0.5rem 1rem',
                background: photos.length === 0 ? '#666' : '#dc3545',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: photos.length === 0 ? 'not-allowed' : 'pointer'
              }}
            >
              {mosaicOperating ? 'Deleting...' : 'Delete All'}
            </button>
            <button
              onClick={() => document.getElementById('upload-input').click()}
              style={{
                padding: '0.5rem 1rem',
                background: '#17a2b8',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              📤 Upload Photos
            </button>
            <button
              onClick={loadPhotos}
              style={{
                padding: '0.5rem 1rem',
                background: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Refresh
            </button>
            <input
              id="upload-input"
              type="file"
              multiple
              accept="image/*"
              onChange={handleFileUpload}
              style={{ display: 'none' }}
            />
          </div>
        </div>

        {/* Photo Count Info */}
        {totalPhotos > 0 && (
          <div style={{
            textAlign: 'center',
            marginBottom: '1rem',
            padding: '0.5rem',
            background: '#333',
            borderRadius: '4px',
            color: '#ccc'
          }}>
            Showing {photos.length} of {totalPhotos} photos
            {totalPhotos > photosPerPage && ` (Page ${currentPage} of ${Math.ceil(totalPhotos / photosPerPage)})`}
          </div>
        )}

        {/* Pagination Controls */}
        {totalPhotos > photosPerPage && !loading && (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '1rem',
            marginBottom: '2rem',
            padding: '1rem',
            background: '#222',
            borderRadius: '8px',
            flexWrap: 'wrap'
          }}>
            <button
              onClick={goToPreviousPage}
              disabled={currentPage === 1}
              style={{
                padding: '0.5rem 1rem',
                background: currentPage === 1 ? '#555' : '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: currentPage === 1 ? 'not-allowed' : 'pointer'
              }}
            >
              Previous
            </button>

            <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
              {/* Page Numbers */}
              {Array.from({ length: Math.ceil(totalPhotos / photosPerPage) }, (_, i) => i + 1)
                .filter(page => {
                  // Show first page, last page, current page, and 2 pages around current
                  const totalPages = Math.ceil(totalPhotos / photosPerPage);
                  return page === 1 ||
                         page === totalPages ||
                         Math.abs(page - currentPage) <= 1;
                })
                .map((page, index, array) => {
                  // Add ellipsis if there's a gap
                  const prevPage = array[index - 1];
                  const showEllipsis = prevPage && page - prevPage > 1;

                  return (
                    <div key={page} style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      {showEllipsis && <span style={{ color: '#ccc' }}>...</span>}
                      <button
                        onClick={() => goToPage(page)}
                        style={{
                          padding: '0.5rem 0.75rem',
                          background: page === currentPage ? '#28a745' : '#007bff',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          cursor: 'pointer',
                          minWidth: '40px'
                        }}
                      >
                        {page}
                      </button>
                    </div>
                  );
                })}

              <span style={{ color: '#ccc', marginLeft: '1rem' }}>
                ({totalPhotos} total photos)
              </span>
            </div>

            <button
              onClick={goToNextPage}
              disabled={currentPage >= Math.ceil(totalPhotos / photosPerPage)}
              style={{
                padding: '0.5rem 1rem',
                background: currentPage >= Math.ceil(totalPhotos / photosPerPage) ? '#555' : '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: currentPage >= Math.ceil(totalPhotos / photosPerPage) ? 'not-allowed' : 'pointer'
              }}
            >
              Next
            </button>
          </div>
        )}

        {/* Load More Button (Alternative to pagination) */}
        {totalPhotos > photos.length && !loading && (
          <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
            <button
              onClick={() => {
                // Load more photos by increasing page size temporarily
                const newPhotosPerPage = photos.length + 12;
                const allPhotosNeeded = Math.min(newPhotosPerPage, totalPhotos);
                const newPage = Math.ceil(allPhotosNeeded / 12);
                goToPage(newPage);
              }}
              style={{
                padding: '0.75rem 2rem',
                background: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '1rem',
                fontWeight: '500'
              }}
            >
              Load More Photos ({totalPhotos - photos.length} remaining)
            </button>
          </div>
        )}

        {photos.length === 0 && totalPhotos === 0 ? (
          <div style={{
            textAlign: 'center',
            color: '#ccc',
            padding: '2rem',
            background: '#222',
            borderRadius: '8px'
          }}>
            No photos uploaded yet
          </div>
        ) : (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
            gap: '1rem'
          }}>
            {photos.map((photo) => (
              <div
                key={photo._id}
                style={{
                  background: '#222',
                  borderRadius: '8px',
                  overflow: 'hidden',
                  border: '1px solid #333'
                }}
              >
                <img
                  src={photo.thumbnailUrl || photo.cloudinaryUrl}
                  alt={`Photo by ${photo.guestName}`}
                  style={{
                    width: '100%',
                    height: '150px',
                    objectFit: 'cover'
                  }}
                />
                <div style={{ padding: '1rem' }}>
                  <h4 style={{ color: '#fff', margin: '0 0 0.5rem 0', fontSize: '0.9rem' }}>
                    {photo.guestName || 'Anonymous'}
                  </h4>
                  <p style={{ color: '#ccc', margin: '0 0 0.5rem 0', fontSize: '0.8rem' }}>
                    Position: ({photo.gridRow + 1}, {photo.gridCol + 1})
                  </p>
                  <p style={{ color: '#ccc', margin: '0 0 1rem 0', fontSize: '0.8rem' }}>
                    {new Date(photo.uploadedAt).toLocaleString()}
                  </p>
                  <button
                    onClick={() => handleDeletePhoto(photo._id)}
                    style={{
                      width: '100%',
                      padding: '0.5rem',
                      background: '#dc3545',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      fontSize: '0.8rem'
                    }}
                  >
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
        </section>
      )}


    </div>
  );
};

export default AdminDashboard;
