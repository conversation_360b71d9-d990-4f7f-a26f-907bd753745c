import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';

import { useSocket } from '../context/SocketContext';
import { photoAPI, gridAPI, handleAPIError } from '../services/api';

const AdminDashboard = () => {
  const { gridState, connected } = useSocket();
  const [photos, setPhotos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [photosPerPage] = useState(12); // Show 12 photos per page
  const [totalPhotos, setTotalPhotos] = useState(0);
  const [paginationLoading, setPaginationLoading] = useState(false);

  const [gridConfig, setGridConfig] = useState({ rows: 10, cols: 10 });
  const [showGridConfig, setShowGridConfig] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [mosaicOperating, setMosaicOperating] = useState(false);


  // Mosaic configuration state
  const [showMosaicConfig, setShowMosaicConfig] = useState(false);
  const [mosaicStatus, setMosaicStatus] = useState(null);
  const [uploadingBaseImage, setUploadingBaseImage] = useState(false);
  const [processingBaseImage, setProcessingBaseImage] = useState(false);


  useEffect(() => {
    // Add admin page class to body for scrolling
    document.body.classList.add('admin-page');

    loadPhotos();
    loadGridConfig();
    loadMosaicStatus();

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('admin-page');
    };
  }, []);

  // Listen for real-time grid updates to refresh admin counters
  useEffect(() => {
    if (!gridState) return;

    // Update local grid config when gridState changes
    if (gridState.gridSize) {
      setGridConfig({
        rows: gridState.gridSize.rows,
        cols: gridState.gridSize.cols
      });
    }

    console.log('📊 Admin: Grid state updated, refreshing stats');
  }, [gridState]);

  useEffect(() => {
    if (gridState?.gridSize) {
      setGridConfig({
        rows: gridState.gridSize.rows,
        cols: gridState.gridSize.cols
      });
    }
  }, [gridState]);

  const loadPhotos = async (page = currentPage) => {
    try {
      setLoading(true);
      const result = await photoAPI.getPhotos(); // Get all photos without filter
      const allPhotos = result.photos || [];

      // Set total count first
      setTotalPhotos(allPhotos.length);

      // If no photos, reset pagination
      if (allPhotos.length === 0) {
        setPhotos([]);
        setCurrentPage(1);
        return;
      }

      // Calculate pagination
      const totalPages = Math.ceil(allPhotos.length / photosPerPage);
      const validPage = Math.min(Math.max(1, page), totalPages);

      const startIndex = (validPage - 1) * photosPerPage;
      const endIndex = startIndex + photosPerPage;
      const paginatedPhotos = allPhotos.slice(startIndex, endIndex);

      setPhotos(paginatedPhotos);
      setCurrentPage(validPage);


    } catch (error) {
      const errorInfo = handleAPIError(error);
      setError(errorInfo.message);
    } finally {
      setLoading(false);
    }
  };



  const loadGridConfig = async () => {
    try {
      const result = await gridAPI.getGridConfig();
      setGridConfig({
        rows: result.gridConfig.rows,
        cols: result.gridConfig.cols
      });
    } catch (error) {
      console.error('Failed to load grid config:', error);
    }
  };

  const updateGridConfig = async () => {
    if (gridConfig.rows < 1 || gridConfig.rows > 50 ||
        gridConfig.cols < 1 || gridConfig.cols > 50) {
      setError('Grid size must be between 1x1 and 50x50');
      return;
    }

    try {
      setUpdating(true);
      setError('');

      await gridAPI.updateGridConfig({
        rows: parseInt(gridConfig.rows),
        cols: parseInt(gridConfig.cols)
      });

      setShowGridConfig(false);

      // Reload all data to update counters
      loadPhotos();
      loadStats();
      loadGridConfig(); // Reload grid config to sync


      console.log('✅ Grid configuration updated successfully');
    } catch (error) {
      const errorInfo = handleAPIError(error);
      setError(errorInfo.message);
    } finally {
      setUpdating(false);
    }
  };



  const loadMosaicStatus = async () => {
    try {
      const response = await fetch('/api/mosaic/status');
      const result = await response.json();
      if (result.success) {
        setMosaicStatus(result.status);
      }
    } catch (error) {
      console.error('Failed to load mosaic status:', error);
    }
  };

  const handleBaseImageUpload = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      setUploadingBaseImage(true);
      setError('');

      const formData = new FormData();
      formData.append('baseImage', file);
      formData.append('description', 'Base image for mosaic');

      const response = await fetch('/api/mosaic/base-image', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.success) {
        await loadMosaicStatus();
        console.log('✅ Base image uploaded successfully');
      } else {
        throw new Error(result.error || 'Upload failed');
      }

      event.target.value = '';
    } catch (error) {
      console.error('Error uploading base image:', error);
      setError('Failed to upload base image: ' + error.message);
    } finally {
      setUploadingBaseImage(false);
    }
  };

  const handleProcessBaseImage = async () => {
    try {
      setProcessingBaseImage(true);
      setError('');

      console.log('🎨 Starting base image processing...');

      const response = await fetch('/api/mosaic/process-base-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const result = await response.json();

      console.log('📋 Processing response:', result);

      if (response.ok && result.success) {
        await loadMosaicStatus();
        console.log(`✅ Base image processed: ${result.totalCells} cells`);

        // Show success message
        const successMsg = `Successfully processed base image into ${result.totalCells} color cells!`;
        console.log(successMsg);
      } else {
        const errorMsg = result.error || result.message || 'Processing failed';
        console.error('❌ Processing failed:', errorMsg);
        throw new Error(errorMsg);
      }
    } catch (error) {
      console.error('❌ Error processing base image:', error);
      setError('Failed to process base image: ' + error.message);
    } finally {
      setProcessingBaseImage(false);
    }
  };

  const handleToggleColorMatching = async () => {
    console.log('📸 Color matching disabled - no image processing');
    alert('Color matching has been disabled to preserve maximum image quality.');
  };

  // Pagination functions
  const goToPage = async (page) => {
    if (page >= 1 && page <= Math.ceil(totalPhotos / photosPerPage) && !paginationLoading) {
      setPaginationLoading(true);
      await loadPhotos(page);
      setPaginationLoading(false);
    }
  };

  const goToPreviousPage = () => {
    if (currentPage > 1 && !paginationLoading) {
      goToPage(currentPage - 1);
    }
  };

  const goToNextPage = () => {
    if (currentPage < Math.ceil(totalPhotos / photosPerPage) && !paginationLoading) {
      goToPage(currentPage + 1);
    }
  };





  const handleDeletePhoto = async (photoId) => {
    if (!confirm('Are you sure you want to delete this photo?')) {
      return;
    }

    try {
      await photoAPI.deletePhoto(photoId);
      setPhotos(photos.filter(photo => photo._id !== photoId));
    } catch (error) {
      const errorInfo = handleAPIError(error);
      setError(errorInfo.message);
    }
  };

  const handleDownloadAll = async () => {
    try {
      setError('');

      const response = await fetch('/api/photos/download', {
        method: 'GET',
      });

      if (!response.ok) {
        throw new Error('Failed to download photos');
      }

      // Create download link
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `mosaic-photos-${new Date().toISOString().split('T')[0]}.zip`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      alert('Photos downloaded successfully!');
    } catch (error) {
      const errorInfo = handleAPIError(error);
      setError(errorInfo.message);
    }
  };

  const handleDeleteAllPhotos = async () => {
    if (!confirm(`Are you sure you want to delete ALL ${photos.length} photos? This action cannot be undone and will remove photos from both the database and Cloudinary storage.`)) {
      return;
    }

    try {
      setMosaicOperating(true);
      setError('');

      const response = await fetch('/api/photos/all', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to delete all photos');
      }

      const result = await response.json();

      // Refresh photos list and stats
      await loadPhotos();



      alert(`Successfully deleted ${result.deletedCount} photos`);
    } catch (error) {
      console.error('Error deleting all photos:', error);
      setError('Failed to delete all photos: ' + error.message);
    } finally {
      setMosaicOperating(false);
    }
  };

  const handleFileUpload = async (event) => {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    try {
      setMosaicOperating(true);
      setError('');

      let successCount = 0;
      let failedFiles = [];

      for (const file of files) {
        try {
          // Use original file - NO compression
          console.log('📸 Using original file for admin upload');

          // Use photoAPI for consistent error handling
          const formData = new FormData();
          formData.append('photo', file);
          formData.append('guestName', 'Admin Upload');

          const result = await photoAPI.uploadPhoto(formData);

          if (result.success) {
            successCount++;
            console.log(`✅ Uploaded: ${file.name}`);
          } else {
            failedFiles.push(file.name);
          }
        } catch (fileError) {
          console.error(`❌ Failed to upload ${file.name}:`, fileError);
          failedFiles.push(file.name);

          // Continue with other files instead of stopping
          continue;
        }
      }

      // Refresh data after all uploads
      await Promise.all([
        loadPhotos(),
,

      ]);

      // Show results
      if (successCount > 0) {
        const message = `Successfully uploaded ${successCount} photo(s)`;
        console.log(`✅ ${message}`);
        if (failedFiles.length === 0) {
          setError(''); // Clear any previous errors
        }
      }

      if (failedFiles.length > 0) {
        const errorMsg = `Failed to upload ${failedFiles.length} file(s): ${failedFiles.join(', ')}`;
        setError(errorMsg);
      }

      // Clear the input
      event.target.value = '';
    } catch (error) {
      console.error('Error in upload process:', error);
      setError('Upload process failed: ' + error.message);
    } finally {
      setMosaicOperating(false);
    }
  };

  if (loading) {
    return (
      <div className="page-container">
        <div className="loading-overlay">
          <div className="loading-content">
            <div className="loading-spinner"></div>
            <p>Loading admin dashboard...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="page-container" style={{ padding: '2rem', background: '#111' }}>
      <motion.header
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        style={{ marginBottom: '2rem' }}
      >
        <h1 style={{ color: '#fff', marginBottom: '1rem' }}>Admin Dashboard</h1>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center', marginBottom: '1rem' }}>
          <span style={{ color: connected ? '#28a745' : '#dc3545' }}>
            {connected ? '🟢 Connected' : '🔴 Disconnected'}
          </span>

        </div>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <button
              onClick={() => setShowGridConfig(!showGridConfig)}
              style={{
                padding: '0.5rem 1rem',
                background: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Configure Grid
            </button>
            <button
              onClick={() => setShowMosaicConfig(!showMosaicConfig)}
              style={{
                padding: '0.5rem 1rem',
                background: '#ff6b35',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              🎨 Mosaic Setup
            </button>

            <button
              onClick={() => window.location.href = '/'}
              style={{
                padding: '0.5rem 1rem',
                background: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              View Mosaic
            </button>
          </div>
        </div>
      </motion.header>

      {error && (
        <div className="error-message" style={{ marginBottom: '2rem' }}>
          {error}
        </div>
      )}

      {/* Grid Configuration Modal */}
      {showGridConfig && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          style={{
            background: '#222',
            padding: '2rem',
            borderRadius: '8px',
            marginBottom: '2rem',
            border: '1px solid #333'
          }}
        >
          <h3 style={{ color: '#fff', marginBottom: '1rem' }}>Grid Configuration</h3>
          <div style={{ display: 'flex', gap: '1rem', alignItems: 'end', marginBottom: '1rem' }}>
            <div>
              <label style={{ color: '#ccc', display: 'block', marginBottom: '0.5rem' }}>
                Rows (1-50)
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={gridConfig.rows}
                onChange={(e) => setGridConfig(prev => ({ ...prev, rows: e.target.value }))}
                style={{
                  padding: '0.5rem',
                  border: '1px solid #555',
                  borderRadius: '4px',
                  background: '#333',
                  color: '#fff',
                  width: '80px'
                }}
                disabled={updating}
              />
            </div>
            <div>
              <label style={{ color: '#ccc', display: 'block', marginBottom: '0.5rem' }}>
                Columns (1-50)
              </label>
              <input
                type="number"
                min="1"
                max="50"
                value={gridConfig.cols}
                onChange={(e) => setGridConfig(prev => ({ ...prev, cols: e.target.value }))}
                style={{
                  padding: '0.5rem',
                  border: '1px solid #555',
                  borderRadius: '4px',
                  background: '#333',
                  color: '#fff',
                  width: '80px'
                }}
                disabled={updating}
              />
            </div>
            <div style={{ display: 'flex', gap: '0.5rem' }}>
              <button
                onClick={updateGridConfig}
                disabled={updating}
                style={{
                  padding: '0.5rem 1rem',
                  background: updating ? '#666' : '#28a745',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: updating ? 'not-allowed' : 'pointer'
                }}
              >
                {updating ? 'Updating...' : 'Update'}
              </button>
              <button
                onClick={() => setShowGridConfig(false)}
                disabled={updating}
                style={{
                  padding: '0.5rem 1rem',
                  background: '#666',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }}
              >
                Cancel
              </button>
            </div>
          </div>
          <p style={{ color: '#ccc', fontSize: '0.9rem', margin: 0 }}>
            Current: {gridState?.gridSize?.rows || 'Loading...'}×{gridState?.gridSize?.cols || 'Loading...'}
            ({gridState?.stats?.totalPhotos || 0} photos, {gridState?.stats?.fillPercentage || 0}% filled)
          </p>
        </motion.div>
      )}

      {/* Mosaic Configuration Panel */}
      {showMosaicConfig && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          style={{
            background: '#222',
            padding: '2rem',
            borderRadius: '8px',
            marginBottom: '2rem',
            border: '1px solid #ff6b35'
          }}
        >
          <h3 style={{ color: '#fff', marginBottom: '1rem' }}>🎨 Mosaic Configuration</h3>

          {/* Current Status */}
          {mosaicStatus && (
            <div style={{
              background: '#333',
              padding: '1rem',
              borderRadius: '4px',
              marginBottom: '1.5rem',
              border: '1px solid #444'
            }}>
              <h4 style={{ color: '#fff', margin: '0 0 0.5rem 0' }}>Current Status</h4>
              <div style={{ color: '#ccc', fontSize: '0.9rem' }}>
                <p>Grid Size: {mosaicStatus.gridSize.rows}×{mosaicStatus.gridSize.cols} ({mosaicStatus.gridSize.total} cells)</p>
                <p>Fill Status: {mosaicStatus.fillStatus.occupied}/{mosaicStatus.fillStatus.available} ({mosaicStatus.fillStatus.percentage}%)</p>
                <p>Base Image: {mosaicStatus.hasBaseImage ? '✅ Uploaded' : '❌ Not uploaded'}</p>
                <p>Color Matching: {mosaicStatus.enableColorMatching ? '✅ Enabled' : '❌ Disabled'}</p>
                <p>Mode: {mosaicStatus.mosaicMode}</p>
              </div>

              {/* Fill Progress Bar */}
              <div style={{ marginTop: '1rem' }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '0.5rem'
                }}>
                  <span style={{ color: '#fff', fontSize: '0.9rem' }}>Grid Fill Progress</span>
                  <span style={{ color: '#ff6b35', fontSize: '0.9rem', fontWeight: 'bold' }}>
                    {mosaicStatus.fillStatus.percentage}%
                  </span>
                </div>
                <div style={{
                  width: '100%',
                  height: '8px',
                  background: '#555',
                  borderRadius: '4px',
                  overflow: 'hidden'
                }}>
                  <div style={{
                    width: `${mosaicStatus.fillStatus.percentage}%`,
                    height: '100%',
                    background: mosaicStatus.fillStatus.percentage > 80 ? '#28a745' :
                               mosaicStatus.fillStatus.percentage > 50 ? '#ffc107' : '#ff6b35',
                    transition: 'width 0.3s ease'
                  }} />
                </div>
              </div>
            </div>
          )}

          {/* Base Image Upload */}
          <div style={{ marginBottom: '1.5rem' }}>
            <h4 style={{ color: '#fff', marginBottom: '0.5rem' }}>1. Upload Base Image</h4>
            <p style={{ color: '#ccc', fontSize: '0.9rem', marginBottom: '1rem' }}>
              Upload the target image that the mosaic will recreate using user photos.
            </p>
            <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
              <input
                type="file"
                accept="image/*"
                onChange={handleBaseImageUpload}
                disabled={uploadingBaseImage}
                style={{
                  padding: '0.5rem',
                  background: '#333',
                  color: '#fff',
                  border: '1px solid #555',
                  borderRadius: '4px'
                }}
              />
              {uploadingBaseImage && <span style={{ color: '#ff6b35' }}>Uploading...</span>}
            </div>
            {mosaicStatus?.baseImage && (
              <div style={{ marginTop: '0.5rem' }}>
                <img
                  src={mosaicStatus.baseImage.cloudinaryUrl}
                  alt="Base image"
                  style={{
                    maxWidth: '200px',
                    maxHeight: '150px',
                    borderRadius: '4px',
                    border: '1px solid #555'
                  }}
                />
              </div>
            )}
          </div>

          {/* Process Base Image */}
          {mosaicStatus?.hasBaseImage && (
            <div style={{ marginBottom: '1.5rem' }}>
              <h4 style={{ color: '#fff', marginBottom: '0.5rem' }}>2. Process Base Image</h4>
              <p style={{ color: '#ccc', fontSize: '0.9rem', marginBottom: '1rem' }}>
                Analyze the base image to extract colors for each grid cell.
              </p>
              <button
                onClick={handleProcessBaseImage}
                disabled={processingBaseImage}
                style={{
                  padding: '0.75rem 1.5rem',
                  background: processingBaseImage ? '#666' : '#ff6b35',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: processingBaseImage ? 'not-allowed' : 'pointer'
                }}
              >
                {processingBaseImage ? 'Processing...' : 'Process Base Image'}
              </button>
            </div>
          )}

          {/* Color Matching Toggle */}
          <div style={{ marginBottom: '1.5rem' }}>
            <h4 style={{ color: '#fff', marginBottom: '0.5rem' }}>3. Enable Color Matching</h4>
            <p style={{ color: '#ccc', fontSize: '0.9rem', marginBottom: '1rem' }}>
              Toggle between color-matched mosaic and simple grid mode.
            </p>
            <div style={{ display: 'flex', gap: '1rem' }}>
              <button
                onClick={() => handleToggleColorMatching(true)}
                disabled={mosaicOperating || !mosaicStatus?.hasBaseImage}
                style={{
                  padding: '0.75rem 1.5rem',
                  background: mosaicStatus?.enableColorMatching ? '#28a745' : '#666',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: (mosaicOperating || !mosaicStatus?.hasBaseImage) ? 'not-allowed' : 'pointer'
                }}
              >
                ✅ Enable Color Matching
              </button>
              <button
                onClick={() => handleToggleColorMatching(false)}
                disabled={mosaicOperating}
                style={{
                  padding: '0.75rem 1.5rem',
                  background: !mosaicStatus?.enableColorMatching ? '#28a745' : '#666',
                  color: 'white',
                  border: 'none',
                  borderRadius: '4px',
                  cursor: mosaicOperating ? 'not-allowed' : 'pointer'
                }}
              >
                ❌ Simple Grid Mode
              </button>
            </div>
          </div>

          {/* Grid Visualization */}
          {mosaicStatus && mosaicStatus.gridSize.total <= 400 && (
            <div style={{ marginBottom: '1.5rem' }}>
              <h4 style={{ color: '#fff', marginBottom: '0.5rem' }}>Grid Fill Map</h4>
              <p style={{ color: '#ccc', fontSize: '0.9rem', marginBottom: '1rem' }}>
                Green = Filled, Orange = Target Color, Gray = Empty
              </p>
              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: `repeat(${mosaicStatus.gridSize.cols}, 1fr)`,
                  gap: '1px',
                  background: '#555',
                  padding: '4px',
                  borderRadius: '4px',
                  maxWidth: '400px',
                  maxHeight: '300px',
                  overflow: 'auto'
                }}
              >
                {Array.from({ length: mosaicStatus.gridSize.total }, (_, index) => {
                  const row = Math.floor(index / mosaicStatus.gridSize.cols);
                  const col = index % mosaicStatus.gridSize.cols;
                  const isOccupied = gridState?.grid?.[row]?.[col] !== null;
                  const targetColor = gridState?.gridMap?.find(cell => cell.row === row && cell.col === col);

                  return (
                    <div
                      key={`${row}-${col}`}
                      style={{
                        aspectRatio: '1',
                        background: isOccupied ? '#28a745' :
                                   (targetColor && mosaicStatus.enableColorMatching) ?
                                   `rgb(${targetColor.r}, ${targetColor.g}, ${targetColor.b})` : '#666',
                        minWidth: '4px',
                        minHeight: '4px',
                        borderRadius: '1px'
                      }}
                      title={`[${row}, ${col}] ${isOccupied ? 'Filled' : 'Empty'}`}
                    />
                  );
                })}
              </div>
            </div>
          )}

          {/* Close Button */}
          <div style={{ textAlign: 'right' }}>
            <button
              onClick={() => setShowMosaicConfig(false)}
              style={{
                padding: '0.5rem 1rem',
                background: '#666',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Close
            </button>
          </div>
        </motion.div>
      )}



      {/* Stats Section */}
      {gridState && (
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          style={{ marginBottom: '2rem' }}
        >
          <h2 style={{ color: '#fff', marginBottom: '1rem' }}>Statistics</h2>
          <div style={{ 
            display: 'grid', 
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
            gap: '1rem' 
          }}>
            <div style={{ 
              background: '#222', 
              padding: '1rem', 
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <h3 style={{ color: '#007bff', margin: '0 0 0.5rem 0' }}>
                {gridState.stats?.totalPhotos || 0}
              </h3>
              <p style={{ color: '#ccc', margin: 0 }}>Total Photos</p>
            </div>
            <div style={{ 
              background: '#222', 
              padding: '1rem', 
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <h3 style={{ color: '#28a745', margin: '0 0 0.5rem 0' }}>
                {gridState.stats?.fillPercentage || 0}%
              </h3>
              <p style={{ color: '#ccc', margin: 0 }}>Grid Filled</p>
            </div>
            <div style={{ 
              background: '#222', 
              padding: '1rem', 
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <h3 style={{ color: '#ffc107', margin: '0 0 0.5rem 0' }}>
                {gridState.gridSize?.rows}×{gridState.gridSize?.cols}
              </h3>
              <p style={{ color: '#ccc', margin: 0 }}>Grid Size</p>
            </div>
          </div>
        </motion.section>
      )}

      {/* Photos Management */}
      {(
        <motion.section
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '1rem',
          flexWrap: 'wrap',
          gap: '1rem'
        }}>
          <h2 style={{ color: '#fff', margin: 0 }}>Photos ({photos.length})</h2>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <button
              onClick={handleDownloadAll}
              disabled={photos.length === 0}
              style={{
                padding: '0.5rem 1rem',
                background: photos.length === 0 ? '#666' : '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: photos.length === 0 ? 'not-allowed' : 'pointer'
              }}
            >
              📥 Download All
            </button>
            <button
              onClick={handleDeleteAllPhotos}
              disabled={photos.length === 0}
              style={{
                padding: '0.5rem 1rem',
                background: photos.length === 0 ? '#666' : '#dc3545',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: photos.length === 0 ? 'not-allowed' : 'pointer'
              }}
            >
              {mosaicOperating ? 'Deleting...' : 'Delete All'}
            </button>
            <button
              onClick={() => document.getElementById('upload-input').click()}
              style={{
                padding: '0.5rem 1rem',
                background: '#17a2b8',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              📤 Upload Photos
            </button>
            <button
              onClick={loadPhotos}
              style={{
                padding: '0.5rem 1rem',
                background: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Refresh
            </button>
            <input
              id="upload-input"
              type="file"
              multiple
              accept="image/*"
              onChange={handleFileUpload}
              style={{ display: 'none' }}
            />
          </div>
        </div>

        {/* Photo Count Info */}
        {totalPhotos > 0 && (
          <div style={{
            textAlign: 'center',
            marginBottom: '1rem',
            padding: '0.5rem',
            background: '#333',
            borderRadius: '4px',
            color: '#ccc'
          }}>
            Showing {photos.length} of {totalPhotos} photos
            {totalPhotos > photosPerPage && ` (Page ${currentPage} of ${Math.ceil(totalPhotos / photosPerPage)})`}
          </div>
        )}

        {/* Pagination Controls */}
        {totalPhotos > photosPerPage && !loading && (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            gap: '1rem',
            marginBottom: '2rem',
            padding: '1rem',
            background: '#222',
            borderRadius: '8px',
            flexWrap: 'wrap'
          }}>
            <button
              onClick={goToPreviousPage}
              disabled={currentPage === 1}
              style={{
                padding: '0.5rem 1rem',
                background: currentPage === 1 ? '#555' : '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: currentPage === 1 ? 'not-allowed' : 'pointer'
              }}
            >
              Previous
            </button>

            <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
              {/* Page Numbers */}
              {Array.from({ length: Math.ceil(totalPhotos / photosPerPage) }, (_, i) => i + 1)
                .filter(page => {
                  // Show first page, last page, current page, and 2 pages around current
                  const totalPages = Math.ceil(totalPhotos / photosPerPage);
                  return page === 1 ||
                         page === totalPages ||
                         Math.abs(page - currentPage) <= 1;
                })
                .map((page, index, array) => {
                  // Add ellipsis if there's a gap
                  const prevPage = array[index - 1];
                  const showEllipsis = prevPage && page - prevPage > 1;

                  return (
                    <div key={page} style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                      {showEllipsis && <span style={{ color: '#ccc' }}>...</span>}
                      <button
                        onClick={() => goToPage(page)}
                        style={{
                          padding: '0.5rem 0.75rem',
                          background: page === currentPage ? '#28a745' : '#007bff',
                          color: 'white',
                          border: 'none',
                          borderRadius: '4px',
                          cursor: 'pointer',
                          minWidth: '40px'
                        }}
                      >
                        {page}
                      </button>
                    </div>
                  );
                })}

              <span style={{ color: '#ccc', marginLeft: '1rem' }}>
                ({totalPhotos} total photos)
              </span>
            </div>

            <button
              onClick={goToNextPage}
              disabled={currentPage >= Math.ceil(totalPhotos / photosPerPage)}
              style={{
                padding: '0.5rem 1rem',
                background: currentPage >= Math.ceil(totalPhotos / photosPerPage) ? '#555' : '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: currentPage >= Math.ceil(totalPhotos / photosPerPage) ? 'not-allowed' : 'pointer'
              }}
            >
              Next
            </button>
          </div>
        )}

        {/* Load More Button (Alternative to pagination) */}
        {totalPhotos > photos.length && !loading && (
          <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
            <button
              onClick={() => {
                // Load more photos by increasing page size temporarily
                const newPhotosPerPage = photos.length + 12;
                const allPhotosNeeded = Math.min(newPhotosPerPage, totalPhotos);
                const newPage = Math.ceil(allPhotosNeeded / 12);
                goToPage(newPage);
              }}
              style={{
                padding: '0.75rem 2rem',
                background: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '1rem',
                fontWeight: '500'
              }}
            >
              Load More Photos ({totalPhotos - photos.length} remaining)
            </button>
          </div>
        )}

        {photos.length === 0 && totalPhotos === 0 ? (
          <div style={{
            textAlign: 'center',
            color: '#ccc',
            padding: '2rem',
            background: '#222',
            borderRadius: '8px'
          }}>
            No photos uploaded yet
          </div>
        ) : (
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fill, minmax(200px, 1fr))',
            gap: '1rem'
          }}>
            {photos.map((photo) => (
              <motion.div
                key={photo._id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                style={{
                  background: '#222',
                  borderRadius: '8px',
                  overflow: 'hidden',
                  border: '1px solid #333'
                }}
              >
                <img
                  src={photo.thumbnailUrl || photo.cloudinaryUrl}
                  alt={`Photo by ${photo.guestName}`}
                  style={{
                    width: '100%',
                    height: '150px',
                    objectFit: 'cover'
                  }}
                />
                <div style={{ padding: '1rem' }}>
                  <h4 style={{ color: '#fff', margin: '0 0 0.5rem 0', fontSize: '0.9rem' }}>
                    {photo.guestName || 'Anonymous'}
                  </h4>
                  <p style={{ color: '#ccc', margin: '0 0 0.5rem 0', fontSize: '0.8rem' }}>
                    Position: ({photo.gridRow + 1}, {photo.gridCol + 1})
                  </p>
                  <p style={{ color: '#ccc', margin: '0 0 1rem 0', fontSize: '0.8rem' }}>
                    {new Date(photo.uploadedAt).toLocaleString()}
                  </p>
                  <button
                    onClick={() => handleDeletePhoto(photo._id)}
                    style={{
                      width: '100%',
                      padding: '0.5rem',
                      background: '#dc3545',
                      color: 'white',
                      border: 'none',
                      borderRadius: '4px',
                      cursor: 'pointer',
                      fontSize: '0.8rem'
                    }}
                  >
                    Delete
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        )}
        </motion.section>
      )}


    </div>
  );
};

export default AdminDashboard;
