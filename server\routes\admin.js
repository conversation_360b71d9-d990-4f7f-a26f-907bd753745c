const express = require('express');
const router = express.Router();
const { Photo, Event } = require('../models');

// POST /api/admin/login - Simple admin authentication
router.post('/login', async (req, res) => {
  try {
    const { password } = req.body;

    if (!password) {
      return res.status(400).json({ error: 'Password is required' });
    }

    const event = await Event.getDefault();

    // Simple password check (in production, use proper authentication)
    if (password === event.adminPassword || password === process.env.ADMIN_PASSWORD) {
      res.json({
        success: true,
        message: 'Admin authenticated successfully',
        token: 'admin-token-' + Date.now(), // Simple token for demo
        eventId: event._id
      });
    } else {
      res.status(401).json({ error: 'Invalid admin password' });
    }
  } catch (error) {
    console.error('Error in admin login:', error);
    res.status(500).json({
      error: 'Login failed',
      message: error.message
    });
  }
});

// GET /api/admin/stats - Get comprehensive statistics
router.get('/stats', async (req, res) => {
  try {
    const event = await Event.getDefault();

    // Get photo statistics
    const photoStats = await Photo.aggregate([
      { $match: { eventId: event._id, isVisible: true } },
      {
        $group: {
          _id: null,
          totalPhotos: { $sum: 1 },
          approvedPhotos: { $sum: { $cond: ['$isApproved', 1, 0] } },
          pendingPhotos: { $sum: { $cond: [{ $not: '$isApproved' }, 1, 0] } },
          avgFileSize: { $avg: '$fileSize' },
          totalFileSize: { $sum: '$fileSize' }
        }
      }
    ]);

    // Get photos by upload time (last 24 hours)
    const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentPhotos = await Photo.countDocuments({
      eventId: event._id,
      isVisible: true,
      uploadedAt: { $gte: last24Hours }
    });

    // Get top contributors
    const topContributors = await Photo.aggregate([
      { $match: { eventId: event._id, isVisible: true } },
      { $group: { _id: '$guestName', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
      { $limit: 5 }
    ]);

    // Get upload timeline (photos per hour for last 24 hours)
    const uploadTimeline = await Photo.aggregate([
      {
        $match: {
          eventId: event._id,
          isVisible: true,
          uploadedAt: { $gte: last24Hours }
        }
      },
      {
        $group: {
          _id: {
            hour: { $hour: '$uploadedAt' },
            date: { $dateToString: { format: '%Y-%m-%d', date: '$uploadedAt' } }
          },
          count: { $sum: 1 }
        }
      },
      { $sort: { '_id.date': 1, '_id.hour': 1 } }
    ]);

    const stats = photoStats[0] || {
      totalPhotos: 0,
      approvedPhotos: 0,
      pendingPhotos: 0,
      avgFileSize: 0,
      totalFileSize: 0
    };

    res.json({
      success: true,
      eventInfo: {
        id: event._id,
        name: event.name,
        isActive: event.isActive,
        createdAt: event.createdAt,
        gridSize: {
          rows: event.gridRows,
          cols: event.gridCols,
          total: event.totalGridSlots
        }
      },
      photoStats: {
        ...stats,
        recentPhotos,
        fillPercentage: event.gridFillPercentage,
        availableSlots: event.availableSlots
      },
      topContributors,
      uploadTimeline,
      systemInfo: {
        serverUptime: process.uptime(),
        nodeVersion: process.version,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error fetching admin statistics:', error);
    res.status(500).json({
      error: 'Failed to fetch statistics',
      message: error.message
    });
  }
});

module.exports = router;
