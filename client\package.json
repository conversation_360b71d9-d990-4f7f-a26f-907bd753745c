{"name": "mosaic-client", "version": "1.0.0", "description": "Frontend for the mosaic wall application", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"axios": "^1.5.0", "browser-image-compression": "^2.0.2", "framer-motion": "^10.16.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "react-webcam": "^7.1.1", "react-window": "^1.8.8", "socket.io-client": "^4.7.2"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "vite": "^4.4.5"}}