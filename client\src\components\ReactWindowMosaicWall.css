/* React Window Mosaic Wall - Full Screen Auto-Fit */
.react-window-mosaic-wall {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  overflow: hidden; /* Critical: No scrollbars */
  margin: 0;
  padding: 0;
}

/* Individual mosaic cell - No gaps */
.mosaic-cell {
  position: relative;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
}

/* Optimized cell content - NO BORDERS for seamless look */
.cell-content {
  width: 100%;
  height: 100%;
  position: relative;
  background: #1a1a1a;
  border: none; /* Removed borders for seamless mosaic */
  box-sizing: border-box;
  overflow: hidden;
}

.cell-content.has-photo {
  background: transparent;
  border: none; /* No borders on photos */
}

.cell-content.empty-cell {
  background: #2a2a2a;
  border: none; /* No borders on empty cells */
}

/* Optimized photo images */
.cell-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center top; /* Keep top, left, right - trim from bottom only */
  display: block;
  border: none;
  margin: 0;
  padding: 0;
  will-change: transform; /* GPU acceleration */
}

/* Optimized empty placeholder - NO BORDERS */
.empty-placeholder {
  width: 100%;
  height: 100%;
  background: #2a2a2a;
  border: none; /* Removed border for seamless look */
  box-sizing: border-box;
  transition: background-color 0.3s ease;
}

/* Color matching mode styles - NO BORDERS */
.cell-content.color-match-mode {
  /* No border styling for seamless look */
}

.cell-content.color-match-mode .empty-placeholder {
  border: none; /* No borders even in color match mode */
  opacity: 0.8;
}

.cell-content.color-match-mode.has-photo {
  border: none; /* No borders on photos */
  /* Subtle glow effect instead of borders */
  box-shadow: 0 0 2px rgba(40, 167, 69, 0.2);
}

/* Loading state */
.mosaic-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #000;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-family: Arial, sans-serif;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #333;
  border-top: 4px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.mosaic-loading p {
  margin: 0;
  font-size: 1.1rem;
  color: #ccc;
}

/* Ensure no gaps or overflow */
.react-window-mosaic-wall * {
  box-sizing: border-box;
}

/* Override any react-window default styles that might cause scrolling */
.react-window-mosaic-wall .ReactVirtualized__Grid {
  overflow: hidden !important;
}

.react-window-mosaic-wall .ReactVirtualized__Grid__innerScrollContainer {
  overflow: hidden !important;
}

/* Fullscreen Controls */
.mosaic-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  gap: 15px;
  align-items: center;
  transition: opacity 0.3s ease;
}

/* Mosaic Status Indicator */
.mosaic-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.status-indicator.color-match {
  border-color: rgba(255, 107, 53, 0.6);
  background: rgba(255, 107, 53, 0.2);
  color: #ff6b35;
}

.status-indicator.simple {
  border-color: rgba(108, 117, 125, 0.6);
  background: rgba(108, 117, 125, 0.2);
  color: #6c757d;
}

.mosaic-controls.fullscreen-controls {
  opacity: 0.8;
}

.mosaic-controls.fullscreen-controls:hover {
  opacity: 1;
}

.fullscreen-btn {
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 12px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.fullscreen-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.1);
}

/* Fullscreen mode adjustments */
.react-window-mosaic-wall.fullscreen {
  cursor: none;
}

.react-window-mosaic-wall.fullscreen:hover {
  cursor: default;
}

/* Hide controls in fullscreen when not hovered */
.react-window-mosaic-wall.fullscreen .mosaic-controls {
  transition: opacity 0.5s ease;
}
