import React, { useState, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Webcam from 'react-webcam';
import { photoAPI, createFormData, handleAPIError } from '../services/api';
import { useSocket } from '../context/SocketContext';
import { useOfflineSupport, useUploadQueue } from '../hooks/useOfflineSupport';
import { useResponsiveDesign } from '../hooks/useResponsiveDesign';
import { compressCanvasImage } from '../utils/imageCompression';

const PhotoCapture = ({ onUploadSuccess, onUploadError, onClose, captureOnly = false }) => {
  const [isModalOpen, setIsModalOpen] = useState(captureOnly); // Auto-open if captureOnly
  const [captureMode, setCaptureMode] = useState(captureOnly ? 'camera' : 'file'); // 'file' or 'camera'
  const [guestName, setGuestName] = useState('');
  const [guestEmail, setGuestEmail] = useState('');
  const [selectedFile, setSelectedFile] = useState(null);
  const [capturedImage, setCapturedImage] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState('');
  const [previewUrl, setPreviewUrl] = useState('');

  const webcamRef = useRef(null);
  const fileInputRef = useRef(null);
  const { notifyPhotoUploading } = useSocket();
  const { isOnline } = useOfflineSupport();
  const { uploadQueue, queueUpload } = useUploadQueue();
  const { isMobile, getResponsiveValue } = useResponsiveDesign();

  // Webcam configuration - Higher quality settings
  const videoConstraints = {
    width: { ideal: 1920, min: 1280 }, // Higher resolution with fallback
    height: { ideal: 1080, min: 720 }, // Higher resolution with fallback
    facingMode: "user",
    frameRate: { ideal: 30, min: 15 }, // Smooth video
    aspectRatio: 16/9 // Maintain aspect ratio
  };

  const openModal = () => {
    setIsModalOpen(true);
    setError('');
    resetForm();
  };

  const closeModal = () => {
    setIsModalOpen(false);
    resetForm();
    if (onClose) onClose();
  };

  const resetForm = () => {
    setGuestName('');
    setGuestEmail('');
    setSelectedFile(null);
    setCapturedImage(null);
    setPreviewUrl('');
    setUploadProgress(0);
    setError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleFileSelect = async (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setError('Please select a valid image file');
        return;
      }

      // Validate file size (50MB limit before compression)
      if (file.size > 50 * 1024 * 1024) {
        setError('File size must be less than 50MB');
        return;
      }

      try {
        setError('Processing image...');

        // Use original file - NO compression
        setSelectedFile(file);
        setCapturedImage(null);
        setError('');

        // Create preview URL
        const url = URL.createObjectURL(file);
        setPreviewUrl(url);

        console.log('📸 Original file used:', {
          size: (file.size / 1024 / 1024).toFixed(2) + ' MB',
          quality: '100% (original)'
        });
      } catch (error) {
        console.error('File processing failed:', error);
        setError('Failed to process image. Please try a different file.');
      }
    }
  };

  const capturePhoto = useCallback(async () => {
    if (webcamRef.current) {
      const imageSrc = webcamRef.current.getScreenshot();

      if (imageSrc) {
        try {
          setError('Processing capture...');

          // Convert data URL to canvas for compression
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          const img = new Image();

          img.onload = async () => {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);

            // NO compression - maximum quality
            const rawFile = await compressCanvasImage(canvas);

            // Convert back to data URL for preview
            const rawUrl = URL.createObjectURL(rawFile);

            setCapturedImage(rawFile);
            setSelectedFile(null);
            setPreviewUrl(rawUrl);
            setError('');
          };

          img.src = imageSrc;
        } catch (error) {
          console.error('Capture compression failed:', error);
          // Fallback to original capture
          setCapturedImage(imageSrc);
          setSelectedFile(null);
          setPreviewUrl(imageSrc);
          setError('');
        }
      }
    }
  }, [webcamRef]);

  const retakePhoto = () => {
    setCapturedImage(null);
    setPreviewUrl('');
  };

  const dataURLtoFile = (dataurl, filename) => {
    const arr = dataurl.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  };

  const handleUpload = async () => {
    if (!selectedFile && !capturedImage) {
      setError('Please select a file or capture a photo');
      return;
    }

    // Name is now optional - use "Anonymous" if not provided
    const finalName = guestName.trim() || 'Anonymous';

    setIsUploading(true);
    setError('');
    setUploadProgress(0);

    try {
      // Notify other users that upload is starting
      notifyPhotoUploading(finalName);

      // Prepare file for upload
      let fileToUpload = selectedFile;
      if (capturedImage && !selectedFile) {
        // If capturedImage is already a File object (compressed), use it directly
        if (capturedImage instanceof File) {
          fileToUpload = capturedImage;
        } else {
          // Fallback for data URL format
          fileToUpload = dataURLtoFile(capturedImage, `capture_${Date.now()}.jpg`);
        }
      }

      // Create form data
      const formData = createFormData(fileToUpload, {
        guestName: finalName,
        guestEmail: guestEmail.trim() || undefined
      });

      // Upload photo
      const result = await photoAPI.uploadPhoto(formData, (progress) => {
        setUploadProgress(progress);
      });

      // Success
      setUploadProgress(100);
      
      if (onUploadSuccess) {
        onUploadSuccess(result);
      }

      // Close modal after short delay
      setTimeout(() => {
        closeModal();
      }, 1000);

    } catch (error) {
      console.error('Upload error:', error);
      const errorInfo = handleAPIError(error);
      setError(errorInfo.message);
      
      if (onUploadError) {
        onUploadError(errorInfo);
      }
    } finally {
      setIsUploading(false);
    }
  };

  const switchMode = (mode) => {
    setCaptureMode(mode);
    setSelectedFile(null);
    setCapturedImage(null);
    setPreviewUrl('');
    setError('');
  };

  return (
    <>
      {/* Upload Button - Only show if not captureOnly */}
      {!captureOnly && (
        <motion.button
          className="upload-button"
          onClick={openModal}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
          title="Upload Photo"
        >
          📸
        </motion.button>
      )}

      {/* Upload Modal */}
      <AnimatePresence>
        {isModalOpen && (
          <motion.div
            className="upload-modal"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={(e) => e.target === e.currentTarget && closeModal()}
          >
            <motion.div
              className={`upload-modal-content ${captureOnly ? 'fullscreen-capture' : ''}`}
              initial={{ scale: captureOnly ? 1 : 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: captureOnly ? 1 : 0.8, opacity: 0 }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
            >
              <h2>{captureOnly ? 'Capture Your Photo' : 'Add Your Photo to the Mosaic'}</h2>

              {/* Mode Selection - Only show if not capture-only */}
              {!captureOnly && (
                <div className="mode-selection">
                  <button
                    className={`mode-btn ${captureMode === 'file' ? 'active' : ''}`}
                    onClick={() => switchMode('file')}
                  >
                    📁 Upload File
                  </button>
                  <button
                    className={`mode-btn ${captureMode === 'camera' ? 'active' : ''}`}
                    onClick={() => switchMode('camera')}
                  >
                    📷 Take Photo
                  </button>
                </div>
              )}

              {/* Error Message */}
              {error && (
                <motion.div
                  className="error-message"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                >
                  {error}
                </motion.div>
              )}

              {/* File Upload Mode - Hidden in capture-only mode */}
              {!captureOnly && captureMode === 'file' && (
                <div className="file-upload-section">
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept="image/*"
                    onChange={handleFileSelect}
                    className="file-input"
                    id="photo-file"
                  />
                  <label htmlFor="photo-file" className="file-input-label">
                    {selectedFile ? selectedFile.name : 'Choose a photo...'}
                  </label>
                </div>
              )}

              {/* Camera Mode - Default in capture-only mode */}
              {(captureOnly || captureMode === 'camera') && (
                <div className="camera-section">
                  {!capturedImage ? (
                    <div className="webcam-container">
                      <Webcam
                        ref={webcamRef}
                        audio={false}
                        screenshotFormat="image/jpeg"
                        videoConstraints={videoConstraints}
                        className="webcam"
                      />
                      <button
                        className="capture-btn"
                        onClick={capturePhoto}
                        type="button"
                      >
                        📸 Capture
                      </button>
                    </div>
                  ) : (
                    <div className="captured-photo">
                      <img src={capturedImage} alt="Captured" className="preview-image" />
                      <button
                        className="retake-btn"
                        onClick={retakePhoto}
                        type="button"
                      >
                        🔄 Retake
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Preview */}
              {previewUrl && (
                <div className="photo-preview">
                  <img src={previewUrl} alt="Preview" className="preview-image" />
                </div>
              )}

              {/* Guest Information Form */}
              <div className="upload-form">
                <div className="form-group">
                  <label className="form-label">Your Name (Optional)</label>
                  <input
                    type="text"
                    value={guestName}
                    onChange={(e) => setGuestName(e.target.value)}
                    className="form-input"
                    placeholder="Enter your name (or leave blank for Anonymous)"
                    maxLength={100}
                    disabled={isUploading}
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">Email (Optional)</label>
                  <input
                    type="email"
                    value={guestEmail}
                    onChange={(e) => setGuestEmail(e.target.value)}
                    className="form-input"
                    placeholder="<EMAIL>"
                    disabled={isUploading}
                  />
                </div>

                {/* Upload Progress */}
                {isUploading && (
                  <div className="upload-progress">
                    <div className="progress-bar">
                      <div 
                        className="progress-fill"
                        style={{ width: `${uploadProgress}%` }}
                      />
                    </div>
                    <span className="progress-text">{uploadProgress}%</span>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="button-group">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={closeModal}
                    disabled={isUploading}
                  >
                    Cancel
                  </button>
                  <button
                    type="button"
                    className="btn btn-primary"
                    onClick={handleUpload}
                    disabled={isUploading || (!selectedFile && !capturedImage)}
                  >
                    {isUploading ? (
                      <>
                        <span className="loading-spinner"></span>
                        Uploading...
                      </>
                    ) : (
                      'Add to Mosaic'
                    )}
                  </button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default PhotoCapture;
