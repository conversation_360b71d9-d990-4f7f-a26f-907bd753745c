const express = require('express');
const router = express.Router();
const { Photo, Event } = require('../models');
const { deleteImage } = require('../config/cloudinary');
const {
  uploadPhoto,
  processPhoto,
  validateUpload,
  handleUploadError
} = require('../middleware/upload');
const archiver = require('archiver');
const https = require('https');
const http = require('http');

// GET /api/photos - Get all photos for current event
router.get('/', async (req, res) => {
  try {
    const event = await Event.getDefault();
    const photos = await Photo.findByEvent(event._id, {
      approved: req.query.approved !== 'false', // Default to approved only
      sort: { uploadedAt: -1 },
      limit: parseInt(req.query.limit) || 0
    });

    res.json({
      success: true,
      photos,
      total: photos.length,
      eventId: event._id,
      gridSize: {
        rows: event.gridRows,
        cols: event.gridCols,
        total: event.totalGridSlots
      }
    });
  } catch (error) {
    console.error('Error fetching photos:', error);
    res.status(500).json({
      error: 'Failed to fetch photos',
      message: error.message
    });
  }
});

// POST /api/photos - Upload new photo
router.post('/',
  validateUpload,
  uploadPhoto,
  handleUploadError,
  processPhoto,
  async (req, res) => {
    try {
      const photo = req.processedPhoto;
      const event = req.event;

      // Emit socket event for real-time updates
      const socketHandler = req.app.get('socketHandler');
      socketHandler.broadcastPhotoAdded(event._id, {
        photo: photo.toJSON(),
        gridPosition: { row: photo.gridRow, col: photo.gridCol },
        eventStats: {
          totalPhotos: event.totalPhotos + 1,
          fillPercentage: Math.round(((event.totalPhotos + 1) / event.totalGridSlots) * 100)
        }
      });

      res.status(201).json({
        success: true,
        message: 'Photo uploaded successfully',
        photo: photo.toJSON(),
        gridPosition: { row: photo.gridRow, col: photo.gridCol }
      });
    } catch (error) {
      console.error('Error in photo upload route:', error);
      res.status(500).json({
        error: 'Failed to complete photo upload',
        message: error.message
      });
    }
  }
);

// PUT /api/photos/:id/approve - Approve a photo (admin only)
router.put('/:id/approve', async (req, res) => {
  try {
    const photo = await Photo.findById(req.params.id);

    if (!photo) {
      return res.status(404).json({ error: 'Photo not found' });
    }

    // Update photo approval status
    photo.isApproved = true;
    await photo.save();

    // Update event statistics
    const event = await Event.findById(photo.eventId);
    if (event) {
      await event.updateStats();
    }

    // Emit socket event for real-time updates
    const socketHandler = req.app.get('socketHandler');
    socketHandler.broadcastPhotoApproved(photo.eventId, {
      photo: photo.toJSON(),
      gridPosition: { row: photo.gridRow, col: photo.gridCol }
    });

    res.json({
      success: true,
      message: 'Photo approved successfully',
      photo: photo.toJSON()
    });
  } catch (error) {
    console.error('Error approving photo:', error);
    res.status(500).json({
      error: 'Failed to approve photo',
      message: error.message
    });
  }
});

// GET /api/photos/download - Download all photos as zip
router.get('/download', async (req, res) => {
  try {
    const event = await Event.getDefault();
    const photos = await Photo.findByEvent(event._id, {
      sort: { uploadedAt: -1 }
    });

    if (photos.length === 0) {
      return res.status(404).json({
        error: 'No photos available for download'
      });
    }

    const archiver = require('archiver');
    const fetch = require('node-fetch');

    // Set response headers for zip download
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename="mosaic-photos-${new Date().toISOString().split('T')[0]}.zip"`);

    // Create zip archive
    const archive = archiver('zip', {
      zlib: { level: 9 } // Maximum compression
    });

    // Pipe archive to response
    archive.pipe(res);

    // Add photos to archive
    for (let i = 0; i < photos.length; i++) {
      const photo = photos[i];
      try {
        // Fetch image from Cloudinary using https module
        const imageBuffer = await new Promise((resolve, reject) => {
          const url = new URL(photo.cloudinaryUrl);
          const client = url.protocol === 'https:' ? https : http;

          client.get(photo.cloudinaryUrl, (response) => {
            if (response.statusCode === 200) {
              const chunks = [];
              response.on('data', chunk => chunks.push(chunk));
              response.on('end', () => resolve(Buffer.concat(chunks)));
            } else {
              reject(new Error(`HTTP ${response.statusCode}`));
            }
          }).on('error', reject);
        });

        const fileName = `photo_${i + 1}_${photo.guestName || 'anonymous'}_${photo.uploadedAt.toISOString().split('T')[0]}.jpg`;
        archive.append(imageBuffer, { name: fileName });
      } catch (error) {
        console.error(`Error downloading photo ${photo._id}:`, error);
        // Continue with other photos
      }
    }

    // Finalize archive
    await archive.finalize();

  } catch (error) {
    console.error('Error creating photo archive:', error);
    res.status(500).json({
      error: 'Failed to create photo archive',
      message: error.message
    });
  }
});

// DELETE /api/photos/all - Delete all photos (admin only)
router.delete('/all', async (req, res) => {
  try {
    const event = await Event.getDefault();
    const photos = await Photo.find({ eventId: event._id });

    if (photos.length === 0) {
      return res.json({
        success: true,
        message: 'No photos to delete',
        deletedCount: 0
      });
    }

    // Delete all photos from Cloudinary
    const deletePromises = photos.map(photo =>
      deleteImage(photo.cloudinaryPublicId).catch(err => {
        console.error(`Failed to delete image ${photo.cloudinaryPublicId}:`, err);
        return null; // Continue with other deletions
      })
    );

    await Promise.all(deletePromises);

    // Delete all photos from database
    const deleteResult = await Photo.deleteMany({ eventId: event._id });

    // Update event statistics
    await event.updateStats();

    // Emit socket event for real-time updates
    const socketHandler = req.app.get('socketHandler');
    if (socketHandler) {
      await socketHandler.broadcastAllPhotosDeleted(event._id.toString());
    }

    res.json({
      success: true,
      message: `Successfully deleted ${deleteResult.deletedCount} photos`,
      deletedCount: deleteResult.deletedCount
    });
  } catch (error) {
    console.error('Error deleting all photos:', error);
    res.status(500).json({
      error: 'Failed to delete all photos',
      message: error.message
    });
  }
});

// DELETE /api/photos/:id - Delete photo (admin only)
router.delete('/:id', async (req, res) => {
  try {
    const photo = await Photo.findById(req.params.id);

    if (!photo) {
      return res.status(404).json({ error: 'Photo not found' });
    }

    // Delete from Cloudinary
    await deleteImage(photo.cloudinaryPublicId);

    // Delete from database
    await Photo.findByIdAndDelete(req.params.id);

    // Update event statistics
    const event = await Event.findById(photo.eventId);
    if (event) {
      await event.updateStats();
    }

    // Emit socket event for real-time updates
    const socketHandler = req.app.get('socketHandler');
    socketHandler.broadcastPhotoDeleted(photo.eventId, {
      photoId: photo._id,
      gridPosition: { row: photo.gridRow, col: photo.gridCol }
    });

    res.json({
      success: true,
      message: 'Photo deleted successfully',
      photoId: photo._id
    });
  } catch (error) {
    console.error('Error deleting photo:', error);
    res.status(500).json({
      error: 'Failed to delete photo',
      message: error.message
    });
  }
});



module.exports = router;
