const { upload } = require('../config/cloudinary');
const { Photo, Event } = require('../models');

// Middleware to handle single photo upload
const uploadPhoto = upload.single('photo');

// Middleware to process uploaded photo
const processPhoto = async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No photo uploaded' });
    }

    // Extract file information from Cloudinary response
    const cloudinaryUrl = req.file.path;

    // Extract public_id from the Cloudinary URL if not available in req.file
    let cloudinaryPublicId = req.file.public_id || req.file.filename;
    if (!cloudinaryPublicId && cloudinaryUrl) {
      // Extract public_id from URL: https://res.cloudinary.com/cloud/image/upload/v1234567890/folder/public_id.jpg
      const urlParts = cloudinaryUrl.split('/');
      const fileWithExtension = urlParts[urlParts.length - 1];
      cloudinaryPublicId = fileWithExtension.split('.')[0]; // Remove extension

      // If it's in a folder, include the folder path
      const folderIndex = urlParts.indexOf('mosaic-photos');
      if (folderIndex !== -1 && folderIndex < urlParts.length - 1) {
        const folderPath = urlParts.slice(folderIndex, -1).join('/');
        cloudinaryPublicId = `${folderPath}/${cloudinaryPublicId}`;
      }
    }

    // Fallback if still no public_id
    if (!cloudinaryPublicId) {
      cloudinaryPublicId = `photo_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;
    }

    const originalName = req.file.originalname;
    const fileSize = req.file.size;
    const mimeType = req.file.mimetype;

    console.log('📁 File info:', {
      cloudinaryUrl,
      cloudinaryPublicId,
      originalName,
      fileSize,
      mimeType,
      reqFileKeys: Object.keys(req.file)
    });

    // Get or create default event
    const event = await Event.getDefault();
    
    // NO color processing - skip color extraction completely
    console.log('📸 Skipping color processing - no image manipulation');
    let colorAvg = '#cccccc'; // Default gray color
    let rgbColor = { r: 204, g: 204, b: 204 };

    // NO color matching - always use simple grid positioning
    console.log('📍 Using simple grid positioning - no color algorithms');
    let gridPosition = await event.getNextAvailablePosition();
    console.log(`📍 Next available position [${gridPosition?.row}, ${gridPosition?.col}]`);

    // Handle grid full scenario
    if (!gridPosition) {
      console.log('⚠️ Grid appears full, checking overflow handling...');

      // Try overflow handling
      gridPosition = await event.handleGridOverflow();

      if (!gridPosition) {
        const stats = event.getGridFillStats();
        return res.status(400).json({
          error: 'Grid is completely full. No available positions.',
          gridFull: true,
          stats: stats
        });
      } else {
        console.log(`🔄 Using overflow position [${gridPosition.row}, ${gridPosition.col}]`);
      }
    }

    // Create photo record
    const photoData = {
      cloudinaryUrl,
      cloudinaryPublicId,
      originalName,
      fileSize,
      mimeType,
      colorAvg: {
        r: rgbColor.r,
        g: rgbColor.g,
        b: rgbColor.b,
        hex: colorAvg
      },
      gridRow: gridPosition.row,
      gridCol: gridPosition.col,
      eventId: event._id,
      guestName: req.body.guestName || 'Anonymous',
      guestEmail: req.body.guestEmail || null,
      isApproved: true, // Always approved for immediate display
      isProcessed: true,
      processedAt: new Date()
    };

    console.log('📝 Creating photo with data:', {
      cloudinaryUrl: !!photoData.cloudinaryUrl,
      cloudinaryPublicId: photoData.cloudinaryPublicId,
      eventId: photoData.eventId,
      gridPosition: [photoData.gridRow, photoData.gridCol]
    });

    const photo = new Photo(photoData);
    await photo.save();

    // Mark grid cell as occupied if using color matching
    if (event.enableColorMatching && event.gridMap) {
      await event.occupyGridCell(gridPosition.row, gridPosition.col, photo._id);
    }

    // Update event statistics
    await event.updateStats();

    // Check if grid is getting full and broadcast warning
    const stats = event.getGridFillStats();
    if (stats.percentage >= 90 && stats.percentage < 100) {
      const socketHandler = req.app.get('socketHandler');
      if (socketHandler) {
        socketHandler.broadcastGridFullWarning(event._id, stats);
      }
    }

    // Broadcast grid status update
    const socketHandler = req.app.get('socketHandler');
    if (socketHandler) {
      socketHandler.broadcastGridStatus(event._id);
    }

    // Add photo data to request for use in route handler
    req.processedPhoto = photo;
    req.event = event;

    next();
  } catch (error) {
    console.error('Error processing photo:', error);
    res.status(500).json({ 
      error: 'Failed to process photo',
      message: error.message 
    });
  }
};

// Middleware to validate upload requirements
const validateUpload = async (req, res, next) => {
  try {
    const event = await Event.getDefault();
    
    // Check if uploads are allowed
    if (!event.allowGuestUploads) {
      return res.status(403).json({ 
        error: 'Photo uploads are currently disabled for this event' 
      });
    }

    // Check if event is active
    if (!event.isActive) {
      return res.status(403).json({ 
        error: 'This event is not currently active' 
      });
    }

    // Check if guest name is required
    if (event.requireGuestName && !req.body.guestName) {
      return res.status(400).json({ 
        error: 'Guest name is required for this event' 
      });
    }

    // Check photo limit per guest (if email provided)
    if (req.body.guestEmail && event.maxPhotosPerGuest) {
      const existingPhotos = await Photo.countDocuments({
        eventId: event._id,
        guestEmail: req.body.guestEmail,
        isVisible: true
      });

      if (existingPhotos >= event.maxPhotosPerGuest) {
        return res.status(400).json({ 
          error: `Maximum ${event.maxPhotosPerGuest} photos allowed per guest`,
          limit: event.maxPhotosPerGuest,
          current: existingPhotos
        });
      }
    }

    req.event = event;
    next();
  } catch (error) {
    console.error('Error validating upload:', error);
    res.status(500).json({ 
      error: 'Failed to validate upload requirements',
      message: error.message 
    });
  }
};

// Error handling middleware for multer
const handleUploadError = (error, req, res, next) => {
  if (error) {
    console.error('Upload error:', error);
    
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({
        error: 'File too large. Maximum size is 50MB.'
      });
    }
    
    if (error.message.includes('Invalid file type')) {
      return res.status(400).json({ 
        error: 'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.' 
      });
    }
    
    return res.status(400).json({ 
      error: 'Upload failed',
      message: error.message 
    });
  }
  
  next();
};

module.exports = {
  uploadPhoto,
  processPhoto,
  validateUpload,
  handleUploadError
};
