const sharp = require('sharp');
const axios = require('axios');
const { extractAverageColor } = require('../config/cloudinary');

/**
 * Extract average color from an image buffer using Sharp
 * @param {Buffer} imageBuffer - Image buffer
 * @returns {Object} RGB color object { r, g, b }
 */
const extractColorFromBuffer = async (imageBuffer) => {
  try {
    // Resize to 1x1 pixel to get average color
    const { data } = await sharp(imageBuffer)
      .resize(1, 1)
      .raw()
      .toBuffer({ resolveWithObject: true });
    
    return {
      r: data[0],
      g: data[1], 
      b: data[2]
    };
  } catch (error) {
    console.error('Error extracting color from buffer:', error);
    return { r: 128, g: 128, b: 128 }; // Default gray
  }
};

/**
 * Extract average color from Cloudinary URL
 * @param {string} cloudinaryUrl - Cloudinary image URL
 * @param {string} publicId - Cloudinary public ID
 * @returns {Object} RGB color object { r, g, b }
 */
const extractColorFromCloudinary = async (cloudinaryUrl, publicId) => {
  try {
    // Try using the existing Cloudinary color extraction first
    const colorHex = await extractAverageColor(cloudinaryUrl, publicId);

    if (typeof colorHex === 'string' && colorHex.startsWith('#')) {
      const hex = colorHex.slice(1);
      return {
        r: parseInt(hex.substr(0, 2), 16),
        g: parseInt(hex.substr(2, 2), 16),
        b: parseInt(hex.substr(4, 2), 16)
      };
    } else if (colorHex && typeof colorHex === 'object') {
      return colorHex;
    }

    // Fallback: download image and extract color manually
    console.log('🔄 Fallback: extracting color manually from', cloudinaryUrl);
    const response = await axios.get(cloudinaryUrl, {
      responseType: 'arraybuffer',
      timeout: 10000
    });
    const imageBuffer = Buffer.from(response.data);
    return await extractColorFromBuffer(imageBuffer);

  } catch (error) {
    console.error('Error extracting color from Cloudinary:', error);
    return { r: 128, g: 128, b: 128 }; // Default gray
  }
};

/**
 * Calculate color distance using weighted RGB formula
 * @param {Object} color1 - First color { r, g, b }
 * @param {Object} color2 - Second color { r, g, b }
 * @returns {number} Color distance
 */
const calculateColorDistance = (color1, color2) => {
  return Math.sqrt(
    0.3 * Math.pow(color1.r - color2.r, 2) +
    0.59 * Math.pow(color1.g - color2.g, 2) +
    0.11 * Math.pow(color1.b - color2.b, 2)
  );
};

/**
 * Convert RGB to hex string
 * @param {Object} rgb - RGB color object { r, g, b }
 * @returns {string} Hex color string
 */
const rgbToHex = (rgb) => {
  const toHex = (n) => Math.round(Math.max(0, Math.min(255, n))).toString(16).padStart(2, '0');
  return `#${toHex(rgb.r)}${toHex(rgb.g)}${toHex(rgb.b)}`;
};

/**
 * Convert hex to RGB object
 * @param {string} hex - Hex color string
 * @returns {Object} RGB color object { r, g, b }
 */
const hexToRgb = (hex) => {
  const cleanHex = hex.replace('#', '');
  return {
    r: parseInt(cleanHex.substr(0, 2), 16),
    g: parseInt(cleanHex.substr(2, 2), 16),
    b: parseInt(cleanHex.substr(4, 2), 16)
  };
};

/**
 * Process base image into grid color map
 * @param {string} imageUrl - Base image URL
 * @param {number} rows - Grid rows
 * @param {number} cols - Grid columns
 * @returns {Array} Array of color data for each grid cell
 */
const processBaseImageToGrid = async (imageUrl, rows, cols) => {
  try {
    console.log(`🎨 Processing base image: ${rows}x${cols} grid from ${imageUrl}`);

    // Download image using axios
    const response = await axios.get(imageUrl, {
      responseType: 'arraybuffer',
      timeout: 30000 // 30 second timeout
    });
    const imageBuffer = Buffer.from(response.data);
    
    // Get image metadata
    const image = sharp(imageBuffer);
    const { width, height } = await image.metadata();
    
    // Calculate tile dimensions
    const tileWidth = Math.floor(width / cols);
    const tileHeight = Math.floor(height / rows);
    
    console.log(`📐 Image: ${width}x${height}, Tiles: ${tileWidth}x${tileHeight}`);
    
    const colorData = [];
    
    // Extract color from each grid cell
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        const left = col * tileWidth;
        const top = row * tileHeight;
        
        // Extract tile and get average color
        const tileBuffer = await image
          .extract({ left, top, width: tileWidth, height: tileHeight })
          .resize(1, 1)
          .raw()
          .toBuffer();
        
        const r = tileBuffer[0];
        const g = tileBuffer[1];
        const b = tileBuffer[2];
        
        colorData.push({ row, col, r, g, b });
      }
    }
    
    console.log(`✅ Extracted colors for ${colorData.length} grid cells`);
    return colorData;
    
  } catch (error) {
    console.error('Error processing base image to grid:', error);
    throw error;
  }
};

module.exports = {
  extractColorFromBuffer,
  extractColorFromCloudinary,
  calculateColorDistance,
  rgbToHex,
  hexToRgb,
  processBaseImageToGrid
};
