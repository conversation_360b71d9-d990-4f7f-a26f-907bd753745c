# 🎨 Mosaic Wall - Real-time Photo Mosaic Application

A sophisticated real-time photo mosaic application built with the MERN stack that allows guests to upload photos which are automatically arranged into beautiful mosaic patterns. Perfect for events, weddings, conferences, and gatherings.

![Mosaic Wall Demo](https://via.placeholder.com/800x400/000000/FFFFFF?text=Mosaic+Wall+Demo)

## ✨ Features

### Core Functionality
- 📸 **Real-time Photo Upload**: Guests can upload photos via webcam or file upload
- 🎨 **Live Mosaic Grid**: Photos appear instantly in a dynamic mosaic pattern
- 🔄 **Real-time Updates**: All connected devices see updates immediately via Socket.IO
- 🌈 **Intelligent Color Matching**: Advanced algorithms arrange photos by color similarity
- 🎯 **Grid Optimization**: Smart algorithms find optimal photo arrangements

### User Experience
- 📱 **Responsive Design**: Optimized for phones, tablets, and large displays
- 🔒 **Offline Support**: Queue uploads when offline, sync when reconnected
- ⚡ **Performance Optimized**: Lazy loading, image optimization, and virtualized grids
- 🎭 **Smooth Animations**: Beautiful transitions and real-time visual feedback
- 👥 **Multi-device Support**: Simultaneous uploads from multiple devices

### Administration
- 👨‍💼 **Admin Dashboard**: Comprehensive management interface
- 🎛️ **Grid Configuration**: Dynamically adjust grid size and layout
- 📊 **Real-time Statistics**: Monitor uploads, grid fill percentage, and color analysis
- 🔧 **Mosaic Tools**: Arrange by color, optimize layout, or shuffle randomly
- 🗑️ **Photo Management**: Review, approve, and delete photos

### Technical Features
- ☁️ **Cloud Storage**: Secure image storage with Cloudinary CDN
- 🔐 **Security**: Rate limiting, input validation, and secure file uploads
- 📈 **Scalable Architecture**: Built to handle multiple concurrent users
- 🔄 **Auto-retry Logic**: Robust error handling and retry mechanisms

## 🏗️ Architecture

### Technology Stack
- **Frontend**: React 18 + Vite, Socket.IO Client, Framer Motion, React Window
- **Backend**: Node.js + Express, Socket.IO Server, Mongoose ODM
- **Database**: MongoDB with aggregation pipelines for analytics
- **Storage**: Cloudinary for optimized image storage and transformation

### Project Structure

```
mosaic/
├── client/                 # React frontend application
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── context/        # React context providers
│   │   ├── hooks/          # Custom React hooks
│   │   ├── services/       # API services and utilities
│   │   └── utils/          # Client-side utilities
│   ├── public/             # Static assets and service worker
│   └── package.json        # Frontend dependencies
├── server/                 # Node.js backend application
│   ├── config/             # Configuration files
│   ├── middleware/         # Express middleware
│   ├── models/             # Mongoose data models
│   ├── routes/             # API route handlers
│   ├── services/           # Business logic services
│   ├── socket/             # Socket.IO event handlers
│   ├── utils/              # Server utilities
│   └── server.js           # Main server entry point
├── .env.example           # Environment variables template
└── README.md              # This documentation
```

## 🚀 Quick Start

### Prerequisites

- **Node.js 18+** and npm
- **MongoDB** (local installation or MongoDB Atlas account)
- **Cloudinary account** for image storage

### Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/mosaic-wall.git
   cd mosaic-wall
   ```

2. **Configure environment variables**
   ```bash
   # Copy environment template
   cp .env.example .env

   # Edit .env with your credentials:
   # - MongoDB connection string
   # - Cloudinary credentials (cloud_name, api_key, api_secret)
   # - Admin password
   # - JWT secret
   ```

3. **Install dependencies**
   ```bash
   # Install backend dependencies
   cd server
   npm install

   # Install frontend dependencies
   cd ../client
   npm install
   ```

4. **Start development servers**
   ```bash
   # Terminal 1: Start backend (from server directory)
   npm run dev

   # Terminal 2: Start frontend (from client directory)
   npm run dev
   ```

5. **Access the application**
   - **Main app**: http://localhost:3000
   - **Admin panel**: http://localhost:3000/admin
   - **API health**: http://localhost:5000/api/health

### Production Deployment

1. **Configure production environment**
   ```bash
   cp .env.production .env
   # Edit .env with your production values
   ```

2. **Build and deploy**
   ```bash
   # Build frontend
   cd client
   npm run build

   # Start backend in production mode
   cd ../server
   NODE_ENV=production npm start
   ```

3. **Access the application**
   - **Main app**: http://localhost (or your domain)
   - **Admin panel**: http://localhost/admin

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `MONGODB_URI` | MongoDB connection string | ✅ | - |
| `CLOUDINARY_CLOUD_NAME` | Cloudinary cloud name | ✅ | - |
| `CLOUDINARY_API_KEY` | Cloudinary API key | ✅ | - |
| `CLOUDINARY_API_SECRET` | Cloudinary API secret | ✅ | - |
| `PORT` | Server port | ❌ | 5000 |
| `NODE_ENV` | Environment mode | ❌ | development |
| `JWT_SECRET` | JWT signing secret | ✅ | - |
| `ADMIN_PASSWORD` | Admin panel password | ❌ | admin123 |
| `DEFAULT_GRID_ROWS` | Default grid rows | ❌ | 10 |
| `DEFAULT_GRID_COLS` | Default grid columns | ❌ | 10 |
| `REACT_APP_API_URL` | Frontend API URL | ❌ | http://localhost:5000 |
| `REACT_APP_SOCKET_URL` | Frontend Socket.IO URL | ❌ | http://localhost:5000 |

### Cloudinary Setup

1. Create a free account at [Cloudinary](https://cloudinary.com/)
2. Go to your Dashboard to find your credentials
3. Add the credentials to your `.env` file:
   ```env
   CLOUDINARY_CLOUD_NAME=your-cloud-name
   CLOUDINARY_API_KEY=your-api-key
   CLOUDINARY_API_SECRET=your-api-secret
   ```

### MongoDB Setup

**Option 1: MongoDB Atlas (Recommended)**
1. Create a free account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a new cluster
3. Get your connection string and add it to `.env`:
   ```env
   MONGODB_URI=mongodb+srv://username:<EMAIL>/mosaic
   ```

**Option 2: Local MongoDB**
```env
MONGODB_URI=mongodb://localhost:27017/mosaic
```

## 🛠️ Development Guide

### Backend Architecture (server/)

**Core Technologies:**
- **Express.js**: RESTful API server with middleware
- **Socket.IO**: Real-time bidirectional communication
- **Mongoose**: MongoDB object modeling with validation
- **Cloudinary**: Image storage and transformation
- **Multer**: File upload handling

**Key Components:**
- `models/`: MongoDB schemas (Photo, Event)
- `routes/`: API endpoints (photos, grid, admin, mosaic)
- `services/`: Business logic (mosaic algorithms, color matching)
- `middleware/`: Upload processing, validation, error handling
- `socket/`: Real-time event management

### Frontend Architecture (client/)

**Core Technologies:**
- **React 18**: Component-based UI with hooks
- **Vite**: Fast build tool and dev server
- **Socket.IO Client**: Real-time updates
- **Framer Motion**: Smooth animations and transitions
- **React Window**: Virtualized grids for performance

**Key Components:**
- `components/`: UI components (PhotoGrid, PhotoCapture, AdminDashboard)
- `hooks/`: Custom hooks (offline support, responsive design, image optimization)
- `context/`: React context (Socket connection, app state)
- `services/`: API communication and error handling

### Color Matching Algorithm

The mosaic uses advanced color analysis:

1. **Color Extraction**: Extract average RGB values from uploaded photos
2. **Color Space Conversion**: Convert RGB to LAB for perceptual accuracy
3. **Distance Calculation**: Use Delta E CIE76 for color similarity
4. **Optimization**: Genetic algorithm for optimal photo placement
5. **Real-time Updates**: Instant visual feedback via Socket.IO

### Performance Optimizations

- **Lazy Loading**: Images load only when visible
- **Image Optimization**: Cloudinary transformations for different screen sizes
- **Virtualized Grids**: Render only visible grid items
- **Caching**: Service worker caches for offline support
- **Compression**: Automatic image compression and format optimization

## 🚀 Deployment

### Production Deployment

**1. Prepare Environment**
```bash
# Copy production environment template
cp .env.production .env

# Edit with your production values
nano .env
```

**2. Build and Deploy**
```bash
# Build Frontend
cd client
npm run build

# Deploy Backend
cd ../server
NODE_ENV=production npm start
```

**3. Serve Frontend**
```bash
# Using any static file server
# Point to client/dist directory
# Example with serve:
npx serve -s client/dist -p 3000
```

### Environment Checklist

- ✅ Set `NODE_ENV=production`
- ✅ Configure production MongoDB URI
- ✅ Set up HTTPS for camera access
- ✅ Configure CORS for production domains
- ✅ Set secure JWT secret
- ✅ Configure rate limiting
- ✅ Set up monitoring and logging
- ✅ Configure backup strategy

### Scaling Considerations

- **Load Balancing**: Use cloud load balancers or reverse proxies
- **Database**: MongoDB replica sets for high availability
- **CDN**: Cloudinary provides global CDN for images
- **Process Management**: Use PM2 for process management
- **Monitoring**: Use tools like PM2, New Relic, or DataDog

## 📡 API Reference

### Photos API
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/photos` | Get all photos with pagination | ❌ |
| `POST` | `/api/photos` | Upload new photo | ❌ |
| `DELETE` | `/api/photos/:id` | Delete specific photo | ✅ Admin |

**Upload Photo Example:**
```javascript
const formData = new FormData();
formData.append('photo', file);
formData.append('guestName', 'John Doe');
formData.append('guestEmail', '<EMAIL>');

fetch('/api/photos', {
  method: 'POST',
  body: formData
});
```

### Grid API
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/grid` | Get grid configuration | ❌ |
| `PUT` | `/api/grid` | Update grid size and settings | ✅ Admin |

### Mosaic API
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/mosaic/analysis/:eventId` | Get color analysis | ❌ |
| `POST` | `/api/mosaic/arrange/:eventId` | Arrange photos by color | ✅ Admin |
| `POST` | `/api/mosaic/optimize/:eventId` | Optimize photo placement | ✅ Admin |
| `POST` | `/api/mosaic/shuffle/:eventId` | Randomly shuffle photos | ✅ Admin |

### Admin API
| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `POST` | `/api/admin/login` | Admin authentication | ❌ |
| `GET` | `/api/admin/stats` | Get comprehensive statistics | ✅ Admin |

## 🔌 Socket.IO Events

### Client → Server Events
| Event | Payload | Description |
|-------|---------|-------------|
| `join-room` | `{ eventId, userInfo }` | Join event room for real-time updates |
| `photo-uploading` | `{ guestName }` | Notify others of upload in progress |
| `admin-action` | `{ action, data, adminToken }` | Admin performing action |
| `request-grid-update` | - | Request current grid state |
| `ping` | - | Heartbeat to check connection |

### Server → Client Events
| Event | Payload | Description |
|-------|---------|-------------|
| `photo-added` | `{ photo, gridPosition, eventStats }` | New photo added to grid |
| `photo-deleted` | `{ photoId, gridPosition }` | Photo removed from grid |
| `grid-updated` | `{ gridSize, eventSettings }` | Grid configuration changed |
| `grid-state` | `{ eventId, grid, gridSize, stats }` | Complete grid state |
| `admin-action` | `{ action, data, timestamp }` | Admin action broadcast |
| `user-joined` | `{ userId, userInfo, timestamp }` | User joined the room |
| `user-left` | `{ userId, reason, timestamp }` | User left the room |
| `error` | `{ message }` | Error notification |
| `pong` | `{ timestamp }` | Heartbeat response |

**Example: Listening for new photos**
```javascript
socket.on('photo-added', (data) => {
  console.log('New photo added:', data.photo);
  console.log('Grid position:', data.gridPosition);
  // Update UI with new photo
});
```

## 🧪 Testing

### Running Tests
```bash
# Backend tests
cd server
npm test

# Frontend tests
cd client
npm test

# E2E tests
npm run test:e2e
```

### Test Coverage
- Unit tests for API endpoints
- Integration tests for Socket.IO events
- Component tests for React components
- E2E tests for complete user flows

## 🐛 Troubleshooting

### Common Issues

**1. Photos not uploading**
- Check Cloudinary credentials in `.env`
- Verify file size limits (10MB max)
- Ensure supported file types (JPEG, PNG, GIF, WebP)

**2. Real-time updates not working**
- Check Socket.IO connection in browser dev tools
- Verify CORS configuration for production
- Check firewall settings for WebSocket connections

**3. Grid not displaying correctly**
- Check MongoDB connection
- Verify grid configuration in admin panel
- Clear browser cache and reload

**4. Performance issues**
- Enable image optimization in Cloudinary
- Check network connection for large grids
- Consider reducing grid size for mobile devices

### Debug Mode
```bash
# Enable debug logging
DEBUG=mosaic:* npm run dev
```

## 🤝 Contributing

We welcome contributions! Please follow these steps:

1. **Fork the repository**
2. **Create a feature branch**
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **Make your changes**
   - Follow existing code style
   - Add tests for new features
   - Update documentation
4. **Test thoroughly**
   ```bash
   npm test
   npm run lint
   ```
5. **Submit a pull request**
   - Describe your changes
   - Include screenshots for UI changes
   - Reference any related issues

### Development Guidelines
- Use TypeScript for new features
- Follow React best practices
- Write comprehensive tests
- Document API changes
- Optimize for performance

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: Check this README and inline code comments
- **Issues**: [GitHub Issues](https://github.com/your-username/mosaic-wall/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/mosaic-wall/discussions)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- [Cloudinary](https://cloudinary.com/) for image storage and optimization
- [MongoDB](https://www.mongodb.com/) for flexible data storage
- [Socket.IO](https://socket.io/) for real-time communication
- [React](https://reactjs.org/) and [Vite](https://vitejs.dev/) for the frontend
- [Express.js](https://expressjs.com/) for the backend framework

## 🔮 Roadmap

- [ ] **AI-powered photo arrangement** using machine learning
- [ ] **Multi-event support** with event switching
- [ ] **Photo filters and effects** before upload
- [ ] **Social media integration** for easy sharing
- [ ] **Analytics dashboard** with detailed insights
- [ ] **Mobile app** for iOS and Android
- [ ] **Video support** for dynamic mosaics
- [ ] **3D mosaic visualization** with WebGL

---

**Built with ❤️ for creating memorable experiences at events**
