/**
 * Generate optimized Cloudinary URLs for different use cases
 */

/**
 * Generate thumbnail URL for mosaic grid
 * @param {string} cloudinaryUrl - Original Cloudinary URL
 * @param {number} size - Thumbnail size (default: 100px)
 * @returns {string} - Optimized thumbnail URL
 */
export const getThumbnailUrl = (cloudinaryUrl, size = 100) => {
  if (!cloudinaryUrl) return null;

  // Extract the public ID from the Cloudinary URL
  const urlParts = cloudinaryUrl.split('/');
  const uploadIndex = urlParts.findIndex(part => part === 'upload');
  
  if (uploadIndex === -1) return cloudinaryUrl;

  // Insert transformation parameters after 'upload'
  const baseUrl = urlParts.slice(0, uploadIndex + 1).join('/');
  const publicIdParts = urlParts.slice(uploadIndex + 1);
  
  // NO transformation - use original image quality
  const transformation = `c_fill,w_${size},h_${size}`;
  
  return `${baseUrl}/${transformation}/${publicIdParts.join('/')}`;
};

/**
 * Generate medium-sized image URL for modal/preview
 * @param {string} cloudinaryUrl - Original Cloudinary URL
 * @param {number} maxWidth - Maximum width (default: 800px)
 * @returns {string} - Optimized medium image URL
 */
export const getMediumUrl = (cloudinaryUrl, maxWidth = 800) => {
  if (!cloudinaryUrl) return null;

  const urlParts = cloudinaryUrl.split('/');
  const uploadIndex = urlParts.findIndex(part => part === 'upload');
  
  if (uploadIndex === -1) return cloudinaryUrl;

  const baseUrl = urlParts.slice(0, uploadIndex + 1).join('/');
  const publicIdParts = urlParts.slice(uploadIndex + 1);
  
  // NO compression - use original quality
  const transformation = `c_limit,w_${maxWidth}`;
  
  return `${baseUrl}/${transformation}/${publicIdParts.join('/')}`;
};

/**
 * Generate full-size optimized URL
 * @param {string} cloudinaryUrl - Original Cloudinary URL
 * @returns {string} - Optimized full-size URL
 */
export const getOptimizedUrl = (cloudinaryUrl) => {
  if (!cloudinaryUrl) return null;

  const urlParts = cloudinaryUrl.split('/');
  const uploadIndex = urlParts.findIndex(part => part === 'upload');
  
  if (uploadIndex === -1) return cloudinaryUrl;

  const baseUrl = urlParts.slice(0, uploadIndex + 1).join('/');
  const publicIdParts = urlParts.slice(uploadIndex + 1);
  
  // NO transformation - use original image
  const transformation = ``;
  
  // Return original URL if no transformation
  return transformation ? `${baseUrl}/${transformation}/${publicIdParts.join('/')}` : cloudinaryUrl;
};

/**
 * Generate responsive image URLs for different screen sizes
 * @param {string} cloudinaryUrl - Original Cloudinary URL
 * @returns {Object} - Object with different sized URLs
 */
export const getResponsiveUrls = (cloudinaryUrl) => {
  if (!cloudinaryUrl) return {};

  return {
    thumbnail: getThumbnailUrl(cloudinaryUrl, 100),
    small: getThumbnailUrl(cloudinaryUrl, 200),
    medium: getMediumUrl(cloudinaryUrl, 800),
    large: getMediumUrl(cloudinaryUrl, 1200),
    original: getOptimizedUrl(cloudinaryUrl)
  };
};

/**
 * Preload image for better performance
 * @param {string} url - Image URL to preload
 * @returns {Promise} - Promise that resolves when image is loaded
 */
export const preloadImage = (url) => {
  return new Promise((resolve, reject) => {
    if (!url) {
      reject(new Error('No URL provided'));
      return;
    }

    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
    img.src = url;
  });
};

/**
 * Batch preload multiple images
 * @param {string[]} urls - Array of image URLs
 * @returns {Promise} - Promise that resolves when all images are loaded
 */
export const preloadImages = async (urls) => {
  const validUrls = urls.filter(Boolean);
  
  try {
    await Promise.all(validUrls.map(url => preloadImage(url)));
    console.log(`✅ Preloaded ${validUrls.length} images`);
  } catch (error) {
    console.warn('⚠️ Some images failed to preload:', error);
  }
};
