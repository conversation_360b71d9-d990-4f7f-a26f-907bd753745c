import { useState, useEffect, useCallback, useMemo } from 'react';
import { FixedSizeGrid as Grid } from 'react-window';
import { motion } from 'framer-motion';
import { useSocket } from '../context/SocketContext';
import BaseImageOverlay from './BaseImageOverlay';
import './ReactWindowMosaicWall.css';

const ReactWindowMosaicWall = () => {
  const { gridState, socket } = useSocket();
  const [photos, setPhotos] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [showBaseImage, setShowBaseImage] = useState(true);
  const [baseImageData, setBaseImageData] = useState(null);
  const [newPhotoAnimations, setNewPhotoAnimations] = useState(new Set());
  const [capturedPhotoPopup, setCapturedPhotoPopup] = useState(null);
  const [dimensions, setDimensions] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  });

  // Grid configuration from admin
  const gridConfig = useMemo(() => {
    if (!gridState?.gridSize) {
      return { rows: 10, cols: 10 }; // Default fallback
    }
    return {
      rows: gridState.gridSize.rows,
      cols: gridState.gridSize.cols
    };
  }, [gridState?.gridSize]);

  // Mosaic settings
  const mosaicSettings = useMemo(() => {
    return {
      enableColorMatching: gridState?.enableColorMatching || false,
      mosaicMode: gridState?.mosaicMode || 'simple',
      hasBaseImage: !!gridState?.baseImage?.cloudinaryUrl
    };
  }, [gridState]);

  // Calculate tiles to fill ENTIRE screen area completely
  const tileConfig = useMemo(() => {
    // Calculate exact dimensions to fill entire screen edge-to-edge
    const tileWidth = dimensions.width / gridConfig.cols;
    const tileHeight = dimensions.height / gridConfig.rows;

    // Return exact calculations to fill complete screen
    return {
      tileWidth,   // Fill entire width
      tileHeight   // Fill entire height
    };
  }, [dimensions, gridConfig]);

  // Handle window resize and fullscreen
  useEffect(() => {
    const handleResize = () => {
      setDimensions({
        width: window.innerWidth,
        height: window.innerHeight
      });
    };

    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    const handleKeyPress = (e) => {
      if (e.key === 'f' || e.key === 'F') {
        toggleFullscreen();
      }
      if (e.key === 'Escape') {
        setShowControls(true);
      }
    };

    window.addEventListener('resize', handleResize);
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('keydown', handleKeyPress);

    return () => {
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('keydown', handleKeyPress);
    };
  }, []);

  // Auto-hide controls in fullscreen
  useEffect(() => {
    if (isFullscreen) {
      const timer = setTimeout(() => {
        setShowControls(false);
      }, 3000); // Hide after 3 seconds

      return () => clearTimeout(timer);
    } else {
      setShowControls(true);
    }
  }, [isFullscreen]);

  // Fetch base image data directly from API as fallback
  useEffect(() => {
    const fetchBaseImage = async () => {
      try {
        const response = await fetch('/api/mosaic/status');
        const data = await response.json();
        if (data.success && data.status?.baseImage) {
          setBaseImageData(data.status.baseImage);
        }
      } catch (error) {
        console.error('Failed to fetch base image:', error);
      }
    };

    fetchBaseImage();
  }, []);

  // Fullscreen functions
  const toggleFullscreen = async () => {
    try {
      if (!document.fullscreenElement) {
        await document.documentElement.requestFullscreen();
      } else {
        await document.exitFullscreen();
      }
    } catch (error) {
      console.error('Fullscreen error:', error);
    }
  };

  const handleMouseMove = () => {
    if (isFullscreen) {
      setShowControls(true);
      // Auto-hide again after 3 seconds
      setTimeout(() => {
        if (document.fullscreenElement) {
          setShowControls(false);
        }
      }, 3000);
    }
  };

  // Load photos from grid state - CRITICAL: Must match admin grid exactly
  useEffect(() => {
    if (!gridState || !gridState.grid) {
      console.log('⚠️ No grid state or grid data available');
      return;
    }

    console.log('🔄 GRID SYNC - Processing grid state:', {
      gridSize: gridState.gridSize,
      gridRows: gridState.grid.length,
      gridCols: gridState.grid[0]?.length,
      adminConfig: gridConfig
    });

    // Verify grid dimensions match admin config
    if (gridState.grid.length !== gridConfig.rows ||
        gridState.grid[0]?.length !== gridConfig.cols) {
      console.warn('⚠️ GRID MISMATCH:', {
        serverGrid: `${gridState.grid.length}x${gridState.grid[0]?.length}`,
        adminConfig: `${gridConfig.rows}x${gridConfig.cols}`
      });
    }

    // Store the 2D grid directly for exact position matching
    setPhotos(gridState.grid);
    setLoading(false);

    console.log('✅ GRID SYNCED - Photos loaded:', {
      gridDimensions: `${gridState.grid.length}x${gridState.grid[0]?.length}`,
      photosCount: gridState.grid.flat().filter(Boolean).length,
      totalSlots: gridState.grid.length * (gridState.grid[0]?.length || 0)
    });
  }, [gridState, gridConfig]);

  // Handle real-time photo updates - Update 2D grid directly
  useEffect(() => {
    if (!socket) return;

    const handlePhotoAdded = (data) => {
      console.log('📸 REAL-TIME: New photo added:', data);

      // Show popup first
      setCapturedPhotoPopup(data.photo);

      // Hide popup after 2 seconds and place in grid
      setTimeout(() => {
        setCapturedPhotoPopup(null);

        setPhotos(prevGrid => {
          if (!Array.isArray(prevGrid) || !prevGrid.length) {
            console.log('⚠️ No previous grid, requesting fresh state');
            socket.emit('get-grid-state', { eventId: 'default' });
            return prevGrid;
          }

          // Create deep copy of 2D grid
          const newGrid = prevGrid.map(row => [...row]);
          const { row, col } = data.gridPosition || {};

          if (row !== undefined && col !== undefined &&
              newGrid[row] && col < newGrid[row].length) {
            newGrid[row][col] = data.photo;
            console.log(`✅ REAL-TIME: Updated grid[${row}][${col}] with new photo`);

            // Trigger animation for new photo
            const photoKey = `${row}-${col}-${data.photo._id}`;
            setNewPhotoAnimations(prev => new Set([...prev, photoKey]));
          }

          return newGrid;
        });
      }, 2000);
    };

    const handlePhotoDeleted = (data) => {
      console.log('🗑️ REAL-TIME: Photo deleted:', data);

      setPhotos(prevGrid => {
        if (!Array.isArray(prevGrid)) return prevGrid;

        const newGrid = prevGrid.map(row => [...row]);
        const { row, col } = data.gridPosition || {};

        if (row !== undefined && col !== undefined &&
            newGrid[row] && newGrid[row][col]) {
          newGrid[row][col] = null;
          console.log(`✅ REAL-TIME: Cleared grid[${row}][${col}]`);
        }

        return newGrid;
      });
    };

    const handleAllPhotosDeleted = () => {
      console.log('🗑️ REAL-TIME: All photos deleted');
      setPhotos(prevGrid => {
        if (!Array.isArray(prevGrid)) return prevGrid;
        return prevGrid.map(row => row.map(() => null));
      });
    };

    const handleGridUpdated = (data) => {
      console.log('🔄 REAL-TIME: Grid configuration updated:', data);
      // Request fresh grid state when admin changes grid size
      socket.emit('get-grid-state', { eventId: 'default' });
    };

    socket.on('photo-added', handlePhotoAdded);
    socket.on('photo-deleted', handlePhotoDeleted);
    socket.on('all-photos-deleted', handleAllPhotosDeleted);
    socket.on('grid-updated', handleGridUpdated);

    return () => {
      socket.off('photo-added', handlePhotoAdded);
      socket.off('photo-deleted', handlePhotoDeleted);
      socket.off('all-photos-deleted', handleAllPhotosDeleted);
      socket.off('grid-updated', handleGridUpdated);
    };
  }, [socket]);

  // Optimized Cell renderer with color matching support and animation
  const Cell = useCallback(({ columnIndex, rowIndex, style }) => {
    // Direct access to 2D grid - CRITICAL for admin sync
    const photo = Array.isArray(photos) && photos[rowIndex] ?
                  photos[rowIndex][columnIndex] : null;

    // Get target color for this cell if color matching is enabled
    const targetColor = mosaicSettings.enableColorMatching && gridState?.gridMap ?
                       gridState.gridMap.find(cell => cell.row === rowIndex && cell.col === columnIndex) : null;

    // Check if this is a new photo for animation
    const photoKey = photo ? `${rowIndex}-${columnIndex}-${photo._id}` : null;
    const isNewPhoto = photoKey && newPhotoAnimations.has(photoKey);

    return (
      <div style={style} className="mosaic-cell">
        <motion.div
          className={`cell-content ${photo ? 'has-photo' : 'empty-cell'} ${mosaicSettings.enableColorMatching ? 'color-match-mode' : ''}`}
          style={{
            backgroundColor: !photo && targetColor ?
              `rgb(${targetColor.r}, ${targetColor.g}, ${targetColor.b})` : undefined
          }}
          initial={isNewPhoto ? { scale: 0.5, opacity: 0 } : false}
          animate={{ scale: 1, opacity: 1 }}
          transition={isNewPhoto ? {
            type: "spring",
            stiffness: 200,
            damping: 15,
            duration: 0.8
          } : { duration: 0 }}
          onAnimationComplete={() => {
            if (isNewPhoto && photoKey) {
              setTimeout(() => {
                setNewPhotoAnimations(prev => {
                  const newSet = new Set(prev);
                  newSet.delete(photoKey);
                  return newSet;
                });
              }, 100);
            }
          }}
        >
          {photo ? (
            <img
              src={photo.thumbnailUrl || photo.cloudinaryUrl}
              alt={`Photo by ${photo.guestName || 'Guest'}`}
              className="cell-image"
              loading="lazy"
              decoding="async"
              style={{
                filter: 'brightness(1.2) contrast(1.2)', // Make images more visible
                opacity: 1
              }}
              onError={(e) => {
                // Multi-level fallback: thumbnail -> main -> JPEG -> PNG
                const currentSrc = e.target.src;
                if (currentSrc.includes('thumbnail') && photo.cloudinaryUrl) {
                  e.target.src = photo.cloudinaryUrl;
                } else if (currentSrc.includes('.webp')) {
                  e.target.src = currentSrc.replace('.webp', '.jpg');
                } else if (currentSrc.includes('.jpg')) {
                  e.target.src = currentSrc.replace('.jpg', '.png');
                } else {
                  e.target.style.display = 'none';
                }
              }}
            />
          ) : (
            <div
              className="empty-placeholder"
              style={{
                backgroundColor: targetColor ?
                  `rgb(${targetColor.r}, ${targetColor.g}, ${targetColor.b})` : '#2a2a2a'
              }}
            />
          )}
        </motion.div>
      </div>
    );
  }, [photos, mosaicSettings, gridState?.gridMap, newPhotoAnimations, setNewPhotoAnimations]);

  if (loading) {
    return (
      <div className="mosaic-loading">
        <div className="loading-spinner" />
        <p>Loading Mosaic Wall...</p>
      </div>
    );
  }

  return (
    <div
      className={`react-window-mosaic-wall ${isFullscreen ? 'fullscreen' : ''}`}
      onMouseMove={handleMouseMove}
    >
      {/* Controls */}
      {showControls && (
        <div className={`mosaic-controls ${isFullscreen ? 'fullscreen-controls' : ''}`}>
          {/* Base Image Toggle */}
          {(gridState?.baseImage || baseImageData) && (
            <button
              onClick={() => setShowBaseImage(!showBaseImage)}
              className="base-image-btn"
              title={showBaseImage ? 'Hide Base Image' : 'Show Base Image'}
              style={{
                background: showBaseImage ? '#28a745' : '#666',
                color: 'white',
                border: 'none',
                borderRadius: '50%',
                width: '50px',
                height: '50px',
                fontSize: '20px',
                cursor: 'pointer',
                marginRight: '10px'
              }}
            >
              🖼️
            </button>
          )}

          {/* Fullscreen Button */}
          <button
            onClick={toggleFullscreen}
            className="fullscreen-btn"
            title={isFullscreen ? 'Exit Fullscreen (F)' : 'Enter Fullscreen (F)'}
          >
            {isFullscreen ? '⛶' : '⛶'}
          </button>
        </div>
      )}

      {/* Captured Photo Popup */}
      {capturedPhotoPopup && (
        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          exit={{ scale: 0, opacity: 0 }}
          style={{
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 9999,
            background: 'white',
            borderRadius: '20px',
            padding: '20px',
            boxShadow: '0 20px 40px rgba(0,0,0,0.3)',
            border: '5px solid #28a745'
          }}
        >
          <div style={{ textAlign: 'center' }}>
            <div style={{
              fontSize: '2rem',
              marginBottom: '10px',
              color: '#28a745'
            }}>
              📸 Photo Captured!
            </div>
            <img
              src={capturedPhotoPopup.cloudinaryUrl}
              alt="Captured photo"
              style={{
                width: '200px',
                height: '200px',
                objectFit: 'cover',
                objectPosition: 'center top',
                borderRadius: '10px',
                border: '2px solid #ddd'
              }}
            />
            <div style={{
              marginTop: '10px',
              color: '#666',
              fontSize: '1rem'
            }}>
              Adding to mosaic...
            </div>
          </div>
        </motion.div>
      )}

      {/* Main Grid Container - Full Screen Coverage */}
      <div style={{
        position: 'relative',
        width: '100%',
        height: '100%',
        background: '#000' // Black background
      }}>
        {/* Photo Grid Layer (Bottom) - Covers entire screen */}
        <Grid
          columnCount={gridConfig.cols}
          columnWidth={tileConfig.tileWidth}
          height={dimensions.height} // Use full screen height
          rowCount={gridConfig.rows}
          rowHeight={tileConfig.tileHeight}
          width={dimensions.width} // Use full screen width
          style={{
            overflow: 'hidden', // No scrollbars
            margin: 0,
            padding: 0,
            border: 'none',
            outline: 'none'
          }}
        >
          {Cell}
        </Grid>

        {/* Base Image Overlay Layer (Top) - Full Screen Coverage */}
        {showBaseImage && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            pointerEvents: 'none' // Allow clicks to pass through to photos
          }}>
            <BaseImageOverlay
              baseImageUrl={gridState?.baseImage?.cloudinaryUrl || baseImageData?.cloudinaryUrl}
              gridRows={gridConfig.rows}
              gridCols={gridConfig.cols}
              tileSize={{
                width: tileConfig.tileWidth,
                height: tileConfig.tileHeight
              }}
              screenDimensions={dimensions}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ReactWindowMosaicWall;
