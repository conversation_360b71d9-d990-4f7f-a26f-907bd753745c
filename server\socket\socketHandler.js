const { Photo, Event } = require('../models');

class SocketHandler {
  constructor(io) {
    this.io = io;
    this.connectedUsers = new Map(); // Track connected users
    this.setupEventHandlers();
  }

  setupEventHandlers() {
    this.io.on('connection', (socket) => {
      console.log(`👤 User connected: ${socket.id}`);
      
      // Store user info
      this.connectedUsers.set(socket.id, {
        socketId: socket.id,
        connectedAt: new Date(),
        currentRoom: null
      });

      // Handle joining event room
      socket.on('join-room', async (data) => {
        try {
          const eventId = data.eventId || 'default';
          const userInfo = data.userInfo || {};
          
          // Leave previous room if any
          const user = this.connectedUsers.get(socket.id);
          if (user && user.currentRoom) {
            socket.leave(user.currentRoom);
          }
          
          // Join new room
          socket.join(eventId);
          
          // Update user info
          this.connectedUsers.set(socket.id, {
            ...user,
            currentRoom: eventId,
            userInfo
          });
          
          console.log(`👤 User ${socket.id} joined room: ${eventId}`);

          // Send current grid state to the new user
          console.log(`📊 Sending grid state to user ${socket.id}...`);
          await this.sendGridState(socket, eventId);
          
          // Notify room about new user
          socket.to(eventId).emit('user-joined', {
            userId: socket.id,
            userInfo,
            timestamp: new Date()
          });
          
          // Send connection confirmation
          socket.emit('room-joined', {
            eventId,
            userId: socket.id,
            timestamp: new Date()
          });
          
        } catch (error) {
          console.error('Error joining room:', error);
          socket.emit('error', { message: 'Failed to join room' });
        }
      });

      // Handle photo upload notification
      socket.on('photo-uploading', (data) => {
        const user = this.connectedUsers.get(socket.id);
        if (user && user.currentRoom) {
          socket.to(user.currentRoom).emit('photo-upload-started', {
            userId: socket.id,
            guestName: data.guestName,
            timestamp: new Date()
          });
        }
      });

      // Handle admin actions
      socket.on('admin-action', async (data) => {
        try {
          const user = this.connectedUsers.get(socket.id);
          if (!user || !user.currentRoom) return;

          // Verify admin privileges (basic check - enhance with proper auth)
          if (!data.adminToken) {
            socket.emit('error', { message: 'Admin authentication required' });
            return;
          }

          // Broadcast admin action to all users in room
          this.io.to(user.currentRoom).emit('admin-action-broadcast', {
            action: data.action,
            data: data.data,
            timestamp: new Date(),
            adminId: socket.id
          });

          console.log(`🔧 Admin action: ${data.action} by ${socket.id}`);
          
        } catch (error) {
          console.error('Error handling admin action:', error);
          socket.emit('error', { message: 'Failed to process admin action' });
        }
      });

      // Handle grid position requests
      socket.on('request-grid-update', async (data) => {
        try {
          const user = this.connectedUsers.get(socket.id);
          if (!user || !user.currentRoom) return;

          await this.sendGridState(socket, user.currentRoom);
          
        } catch (error) {
          console.error('Error sending grid update:', error);
          socket.emit('error', { message: 'Failed to get grid update' });
        }
      });

      // Handle heartbeat/ping
      socket.on('ping', () => {
        socket.emit('pong', { timestamp: new Date() });
      });

      // Handle disconnection
      socket.on('disconnect', (reason) => {
        console.log(`👤 User disconnected: ${socket.id} (${reason})`);
        
        const user = this.connectedUsers.get(socket.id);
        if (user && user.currentRoom) {
          // Notify room about user leaving
          socket.to(user.currentRoom).emit('user-left', {
            userId: socket.id,
            reason,
            timestamp: new Date()
          });
        }
        
        // Remove user from tracking
        this.connectedUsers.delete(socket.id);
      });
    });
  }

  // Send current grid state to a socket
  async sendGridState(socket, eventId) {
    try {
      console.log(`🔍 Getting event for ID: ${eventId}`);
      const event = eventId === 'default'
        ? await Event.getDefault()
        : await Event.findById(eventId);

      if (!event) {
        console.log(`❌ Event not found for ID: ${eventId}`);
        socket.emit('error', { message: 'Event not found' });
        return;
      }

      console.log(`📷 Getting photos for event: ${event._id}`);
      const photos = await Photo.findByEvent(event._id, { approved: true });
      console.log(`📊 Found ${photos.length} approved photos`);
      
      // Create grid matrix
      const grid = Array(event.gridRows).fill(null).map(() => 
        Array(event.gridCols).fill(null)
      );
      
      // Fill grid with photos
      photos.forEach(photo => {
        if (photo.gridRow !== undefined && photo.gridCol !== undefined) {
          if (photo.gridRow < event.gridRows && photo.gridCol < event.gridCols) {
            grid[photo.gridRow][photo.gridCol] = photo.toJSON();
          }
        }
      });

      const gridStateData = {
        eventId: event._id,
        grid,
        gridSize: {
          rows: event.gridRows,
          cols: event.gridCols
        },
        stats: {
          totalPhotos: event.totalPhotos,
          fillPercentage: event.gridFillPercentage
        },
        manualColors: Object.fromEntries(event.manualColors || new Map()),
        baseImage: event.baseImage ? {
          cloudinaryUrl: event.baseImage.cloudinaryUrl,
          publicId: event.baseImage.publicId
        } : null,
        timestamp: new Date()
      };

      console.log(`📤 Sending grid state:`, {
        eventId: gridStateData.eventId,
        gridSize: gridStateData.gridSize,
        photosInGrid: photos.length,
        stats: gridStateData.stats,
        hasBaseImage: !!gridStateData.baseImage,
        baseImageUrl: gridStateData.baseImage?.cloudinaryUrl
      });

      socket.emit('grid-state', gridStateData);
      
    } catch (error) {
      console.error('Error sending grid state:', error);
      socket.emit('error', { message: 'Failed to get grid state' });
    }
  }

  // Broadcast photo added to all users in event room
  async broadcastPhotoAdded(eventId, photoData) {
    console.log('🚀 Broadcasting photo added immediately:', photoData);

    // Optimize photo data for frontend
    const optimizedPhotoData = {
      ...photoData,
      photo: {
        ...photoData.photo,
        // Add thumbnail URL for immediate display
        thumbnailUrl: this.generateThumbnailUrl(photoData.photo.cloudinaryUrl),
        // Add responsive URLs
        responsiveUrls: this.generateResponsiveUrls(photoData.photo.cloudinaryUrl)
      },
      timestamp: new Date(),
      immediate: true
    };

    // Emit immediate photo-added event with optimized data
    this.io.to(eventId.toString()).emit('photo-added', optimizedPhotoData);

    // Also emit to all connected sockets for maximum coverage
    this.io.emit('photo-added', optimizedPhotoData);

    // Send updated grid state immediately for instant display
    try {
      const event = await Event.findById(eventId);
      if (event) {
        const photos = await Photo.findByEvent(eventId, { approved: true });

        // Create grid matrix with optimized photo data
        const grid = Array(event.gridRows).fill(null).map(() =>
          Array(event.gridCols).fill(null)
        );

        // Fill grid with photos and add optimized URLs
        photos.forEach(photo => {
          if (photo.gridRow !== undefined && photo.gridCol !== undefined) {
            if (photo.gridRow < event.gridRows && photo.gridCol < event.gridCols) {
              const photoJson = photo.toJSON();
              grid[photo.gridRow][photo.gridCol] = {
                ...photoJson,
                thumbnailUrl: this.generateThumbnailUrl(photoJson.cloudinaryUrl),
                responsiveUrls: this.generateResponsiveUrls(photoJson.cloudinaryUrl)
              };
            }
          }
        });

        const gridStateData = {
          eventId: event._id,
          grid,
          gridSize: {
            rows: event.gridRows,
            cols: event.gridCols
          },
          stats: {
            totalPhotos: event.totalPhotos,
            fillPercentage: event.gridFillPercentage
          },
          baseImage: event.baseImage ? {
            cloudinaryUrl: event.baseImage.cloudinaryUrl,
            publicId: event.baseImage.publicId
          } : null,
          timestamp: new Date(),
          immediate: true,
          optimized: true // Flag for optimized data
        };

        console.log('📤 Sending immediate optimized grid state update');
        // Emit updated grid state immediately
        this.io.to(eventId.toString()).emit('grid-state', gridStateData);

        // Also emit to all connected sockets for maximum coverage
        this.io.emit('grid-state', gridStateData);
      }
    } catch (error) {
      console.error('Error broadcasting updated grid state:', error);
    }
  }

  // Broadcast photo deleted to all users in event room
  broadcastPhotoDeleted(eventId, photoData) {
    this.io.to(eventId.toString()).emit('photo-deleted', {
      ...photoData,
      timestamp: new Date()
    });
  }

  // Broadcast all photos deleted to all users in event room
  broadcastAllPhotosDeleted(eventId) {
    this.io.to(eventId.toString()).emit('all-photos-deleted', {
      eventId,
      timestamp: new Date()
    });
  }

  // Broadcast photo approved to all users in event room
  broadcastPhotoApproved(eventId, photoData) {
    this.io.to(eventId.toString()).emit('photo-approved', {
      ...photoData,
      timestamp: new Date()
    });
  }

  // Broadcast grid configuration update with fresh grid state
  async broadcastGridUpdated(eventId, gridConfig) {
    console.log('🔄 Broadcasting grid update:', gridConfig);

    // Emit immediate grid update notification
    this.io.to(eventId.toString()).emit('grid-updated', {
      ...gridConfig,
      timestamp: new Date()
    });

    // Also emit to all sockets for maximum coverage
    this.io.emit('grid-updated', {
      ...gridConfig,
      timestamp: new Date()
    });

    // Send fresh grid state to all connected clients
    try {
      const event = await Event.findById(eventId);
      if (event) {
        const photos = await Photo.findByEvent(eventId, { approved: true });

        // Create fresh grid matrix
        const grid = Array(event.gridRows).fill(null).map(() =>
          Array(event.gridCols).fill(null)
        );

        // Fill grid with photos
        photos.forEach(photo => {
          if (photo.gridRow !== undefined && photo.gridCol !== undefined) {
            if (photo.gridRow < event.gridRows && photo.gridCol < event.gridCols) {
              grid[photo.gridRow][photo.gridCol] = photo.toJSON();
            }
          }
        });

        const freshGridState = {
          eventId: event._id,
          grid,
          gridSize: {
            rows: event.gridRows,
            cols: event.gridCols
          },
          stats: {
            totalPhotos: event.totalPhotos,
            fillPercentage: event.gridFillPercentage
          },
          baseImage: event.baseImage ? {
            cloudinaryUrl: event.baseImage.cloudinaryUrl,
            publicId: event.baseImage.publicId
          } : null,
          timestamp: new Date(),
          gridUpdated: true
        };

        // Broadcast fresh grid state
        this.io.to(eventId.toString()).emit('grid-state', freshGridState);
        this.io.emit('grid-state', freshGridState);

        console.log('✅ Fresh grid state broadcasted after config update');
      }
    } catch (error) {
      console.error('❌ Error broadcasting fresh grid state:', error);
    }
  }

  // Broadcast admin action
  broadcastAdminAction(eventId, action, data) {
    this.io.to(eventId.toString()).emit('admin-action', {
      action,
      data,
      timestamp: new Date()
    });
  }

  // Broadcast mosaic settings update
  broadcastMosaicSettingsUpdated(eventId, settings) {
    console.log(`🎨 Broadcasting mosaic settings update:`, settings);
    this.io.to(eventId.toString()).emit('mosaic-settings-updated', {
      settings,
      timestamp: new Date()
    });
  }

  // Broadcast grid status update
  async broadcastGridStatus(eventId) {
    try {
      const event = await Event.findById(eventId);
      if (!event) return;

      const stats = event.getGridFillStats();

      console.log(`📊 Broadcasting grid status:`, stats);
      this.io.to(eventId.toString()).emit('grid-status-updated', {
        stats,
        enableColorMatching: event.enableColorMatching,
        mosaicMode: event.mosaicMode,
        timestamp: new Date()
      });
    } catch (error) {
      console.error('Error broadcasting grid status:', error);
    }
  }

  // Broadcast grid full warning
  broadcastGridFullWarning(eventId, stats) {
    console.log(`⚠️ Broadcasting grid full warning:`, stats);
    this.io.to(eventId.toString()).emit('grid-full-warning', {
      stats,
      message: 'Grid is nearly full! Photos may start replacing older ones.',
      timestamp: new Date()
    });
  }

  // Get connection statistics
  getStats() {
    const roomStats = {};

    this.connectedUsers.forEach(user => {
      if (user.currentRoom) {
        roomStats[user.currentRoom] = (roomStats[user.currentRoom] || 0) + 1;
      }
    });

    return {
      totalConnections: this.connectedUsers.size,
      roomStats,
      timestamp: new Date()
    };
  }

  // Generate optimized Cloudinary thumbnail URL
  generateThumbnailUrl(cloudinaryUrl, size = 100) {
    if (!cloudinaryUrl) return null;

    const urlParts = cloudinaryUrl.split('/');
    const uploadIndex = urlParts.findIndex(part => part === 'upload');

    if (uploadIndex === -1) return cloudinaryUrl;

    const baseUrl = urlParts.slice(0, uploadIndex + 1).join('/');
    const publicIdParts = urlParts.slice(uploadIndex + 1);

    // Optimized transformation for thumbnails
    const transformation = `c_fill,w_${size},h_${size},f_auto,q_auto:low,dpr_auto`;

    return `${baseUrl}/${transformation}/${publicIdParts.join('/')}`;
  }

  // Generate responsive Cloudinary URLs
  generateResponsiveUrls(cloudinaryUrl) {
    if (!cloudinaryUrl) return {};

    return {
      thumbnail: this.generateThumbnailUrl(cloudinaryUrl, 100),
      small: this.generateThumbnailUrl(cloudinaryUrl, 200),
      medium: this.generateOptimizedUrl(cloudinaryUrl, 800),
      large: this.generateOptimizedUrl(cloudinaryUrl, 1200),
      original: this.generateOptimizedUrl(cloudinaryUrl)
    };
  }

  // Generate optimized Cloudinary URL
  generateOptimizedUrl(cloudinaryUrl, maxWidth = null) {
    if (!cloudinaryUrl) return null;

    const urlParts = cloudinaryUrl.split('/');
    const uploadIndex = urlParts.findIndex(part => part === 'upload');

    if (uploadIndex === -1) return cloudinaryUrl;

    const baseUrl = urlParts.slice(0, uploadIndex + 1).join('/');
    const publicIdParts = urlParts.slice(uploadIndex + 1);

    // Build transformation string
    let transformation = 'f_auto,q_auto:good,dpr_auto';
    if (maxWidth) {
      transformation = `c_limit,w_${maxWidth},${transformation}`;
    }

    return `${baseUrl}/${transformation}/${publicIdParts.join('/')}`;
  }
}

module.exports = SocketHandler;
