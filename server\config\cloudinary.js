const cloudinary = require('cloudinary').v2;
const { CloudinaryStorage } = require('multer-storage-cloudinary');
const multer = require('multer');

// Configure Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

// Configure Cloudinary storage for Multer - NO COMPRESSION
const storage = new CloudinaryStorage({
  cloudinary: cloudinary,
  params: {
    folder: 'mosaic-photos',
    allowed_formats: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    // NO transformation - upload original files
    public_id: (req, file) => {
      // Generate unique filename with timestamp
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 8);
      return `photo_${timestamp}_${randomString}`;
    }
  }
});

// Configure Multer - NO file size limits for maximum quality
const upload = multer({
  storage: storage,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit for high quality images
    files: 1 // Only one file at a time
  },
  fileFilter: (req, file, cb) => {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'), false);
    }
  }
});

// Helper function to delete image from Cloudinary
const deleteImage = async (publicId) => {
  try {
    const result = await cloudinary.uploader.destroy(publicId);
    return result;
  } catch (error) {
    console.error('Error deleting image from Cloudinary:', error);
    throw error;
  }
};

// Helper function to get image info
const getImageInfo = async (publicId) => {
  try {
    const result = await cloudinary.api.resource(publicId);
    return result;
  } catch (error) {
    console.error('Error getting image info from Cloudinary:', error);
    throw error;
  }
};

// Helper function to generate transformation URL
const generateTransformationUrl = (publicId, transformations) => {
  return cloudinary.url(publicId, transformations);
};

// NO color extraction - disabled for maximum image quality
const extractAverageColor = async (imageUrl, publicId = null) => {
  console.log('📸 Color extraction disabled - returning default color');
  return { r: 128, g: 128, b: 128, hex: '#808080' };
};

module.exports = {
  cloudinary,
  upload,
  deleteImage,
  getImageInfo,
  generateTransformationUrl,
  extractAverageColor
};
