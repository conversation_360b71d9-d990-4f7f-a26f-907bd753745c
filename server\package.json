{"name": "mosaic-server", "version": "1.0.0", "description": "Backend server for the mosaic wall application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["mosaic", "photo", "real-time", "socket.io"], "author": "", "license": "ISC", "dependencies": {"archiver": "^7.0.1", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.40.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^6.10.0", "form-data": "^4.0.3", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "multer": "^1.4.5-lts.1", "multer-storage-cloudinary": "^4.0.0", "node-fetch": "^3.3.2", "sharp": "^0.34.2", "socket.io": "^4.7.2"}, "devDependencies": {"nodemon": "^3.0.1"}}