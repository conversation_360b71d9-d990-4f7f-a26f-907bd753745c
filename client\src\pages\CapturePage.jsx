import { useState, useRef, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Webcam from 'react-webcam';

import { photoAPI, createFormData, handleAPIError } from '../services/api';
import { useSocket } from '../context/SocketContext';

const CapturePage = () => {
  const navigate = useNavigate();
  const [guestName, setGuestName] = useState('');
  const [guestEmail, setGuestEmail] = useState('');
  const [capturedImage, setCapturedImage] = useState(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [facingMode, setFacingMode] = useState('user'); // 'user' for front, 'environment' for back
  const [showCameraOptions, setShowCameraOptions] = useState(false);
  const [cameraActive, setCameraActive] = useState(false);

  const webcamRef = useRef(null);
  const fileInputRef = useRef(null);
  const { notifyPhotoUploading } = useSocket();

  // Add capture page class to body for scrolling
  useEffect(() => {
    document.body.classList.add('capture-page');
    return () => {
      document.body.classList.remove('capture-page');
    };
  }, []);

  // Webcam configuration optimized for head/face capture
  const videoConstraints = {
    width: { ideal: 1280, min: 640 },
    height: { ideal: 720, min: 480 },
    facingMode: facingMode,
    aspectRatio: { ideal: 1.33 }, // 4:3 ratio better for portraits
    frameRate: { ideal: 30, max: 30 }
  };

  // Convert data URL to File
  const dataURLtoFile = (dataurl, filename) => {
    const arr = dataurl.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  };



  const capturePhoto = useCallback(() => {
    const imageSrc = webcamRef.current.getScreenshot();
    setCapturedImage(imageSrc);
  }, [webcamRef]);

  const retakePhoto = () => {
    setCapturedImage(null);
    setError('');
    setSuccess(false);
  };

  const switchCamera = () => {
    setFacingMode(prev => prev === 'user' ? 'environment' : 'user');
    setShowCameraOptions(false);
  };

  // Open camera function for mobile/desktop
  const openCamera = () => {
    // For mobile devices, trigger file input with camera
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
    // Also activate webcam if available
    setCameraActive(true);
  };

  // Handle file input from camera/gallery
  const handleFileInput = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      // Convert file to data URL for preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setCapturedImage(e.target.result);
      };
      reader.readAsDataURL(file);
    } catch (error) {
      console.error('Error reading file:', error);
      setError('Failed to read selected image');
    }
  };

  const resetForm = () => {
    setCapturedImage(null);
    setGuestName('');
    setGuestEmail('');
    setError('');
    setSuccess(false);
    setUploadProgress(0);
  };

  const handleSubmit = async () => {
    if (!capturedImage) {
      setError('Please capture a photo first');
      return;
    }

    setIsUploading(true);
    setError('');
    setUploadProgress(0);

    try {
      const finalName = guestName.trim() || 'Anonymous';
      
      // Notify other users that upload is starting
      notifyPhotoUploading(finalName);

      // Convert captured image to file - NO compression
      const fileToUpload = dataURLtoFile(capturedImage, `capture_${Date.now()}.jpg`);

      console.log('📸 Using original capture:', {
        size: (fileToUpload.size / 1024 / 1024).toFixed(2) + ' MB',
        quality: '100% (no compression)'
      });

      // Create form data
      const formData = createFormData(fileToUpload, {
        guestName: finalName,
        guestEmail: guestEmail.trim() || undefined
      });

      // Upload photo
      await photoAPI.uploadPhoto(formData, (progress) => {
        setUploadProgress(progress);
      });

      // Success
      setUploadProgress(100);
      setSuccess(true);

      // Reset form after success (no redirect)
      setTimeout(() => {
        resetForm();
      }, 3000);

    } catch (error) {
      console.error('Upload error:', error);
      const errorInfo = handleAPIError(error);
      setError(errorInfo.message);
    } finally {
      setIsUploading(false);
    }
  };

  const goBack = () => {
    navigate('/');
  };

  // Remove the success redirect page - we'll show success inline

  return (
    <div className="capture-page-form">
      {/* Header */}
      <div className="capture-header">
        <button className="back-btn-simple" onClick={goBack}>
          ← Back to Wall
        </button>
        <h1>Add Your Photo</h1>
      </div>

      {/* Form Container */}
      <div className="capture-form-container">
        <div className="capture-card">
          {!capturedImage ? (
            // Camera Section
            <div className="camera-form-section">
              <div className="camera-header">
                <h2>📸 Take Your Photo</h2>
                <div className="camera-controls">
                  <button
                    className="camera-switch-btn"
                    onClick={switchCamera}
                    title="Switch Camera"
                  >
                    🔄 {facingMode === 'user' ? 'Back Cam' : 'Front Cam'}
                  </button>
                </div>
              </div>

              {/* Mobile Camera Access Button */}
              <div className="mobile-camera-access" style={{
                textAlign: 'center',
                margin: '1rem 0'
              }}>
                <button
                  className="open-camera-btn"
                  onClick={openCamera}
                  title="Open Camera (Mobile/Desktop)"
                  style={{
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    color: 'white',
                    border: 'none',
                    padding: '12px 24px',
                    borderRadius: '25px',
                    fontSize: '16px',
                    fontWeight: '600',
                    cursor: 'pointer',
                    boxShadow: '0 4px 15px rgba(102, 126, 234, 0.3)',
                    transition: 'all 0.3s ease',
                    display: 'inline-flex',
                    alignItems: 'center',
                    gap: '8px'
                  }}
                  onMouseOver={(e) => {
                    e.target.style.transform = 'translateY(-2px)';
                    e.target.style.boxShadow = '0 6px 20px rgba(102, 126, 234, 0.4)';
                  }}
                  onMouseOut={(e) => {
                    e.target.style.transform = 'translateY(0)';
                    e.target.style.boxShadow = '0 4px 15px rgba(102, 126, 234, 0.3)';
                  }}
                >
                  📱 Open Camera
                </button>

                {/* Hidden file input for camera access */}
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  capture="environment"
                  onChange={handleFileInput}
                  style={{ display: 'none' }}
                />
              </div>

              <div className="webcam-form-container">
                <Webcam
                  ref={webcamRef}
                  audio={false}
                  screenshotFormat="image/jpeg"
                  videoConstraints={videoConstraints}
                  className="webcam-form"
                  key={facingMode} // Force re-render when camera changes
                />
              </div>

              <button
                className="capture-btn-form"
                onClick={capturePhoto}
                disabled={isUploading}
              >
                📸 Capture Photo
              </button>
            </div>
          ) : (
            // Form Section
            <div className="form-section">
              {success ? (
                <div className="success-message-form">
                  <div className="success-icon-form">✅</div>
                  <h2>Photo Added Successfully!</h2>
                  <p>Your photo is now live on the mosaic wall!</p>
                  <button
                    className="take-another-btn"
                    onClick={resetForm}
                  >
                    📸 Take Another Photo
                  </button>
                </div>
              ) : (
                <>
                  <h2>✅ Photo Captured!</h2>

                  {/* Photo Preview */}
                  <div className="photo-preview-form">
                    <img src={capturedImage} alt="Captured" className="preview-img-form" />
                    <button
                      className="retake-btn-form"
                      onClick={retakePhoto}
                      disabled={isUploading}
                    >
                      🔄 Retake
                    </button>
                  </div>

                  {/* Error Message */}
                  {error && (
                    <div className="error-message-form">
                      {error}
                    </div>
                  )}

                  {/* Form Fields */}
                  <div className="form-fields">
                    <div className="form-group-simple">
                      <label>Your Name (Optional)</label>
                      <input
                        type="text"
                        value={guestName}
                        onChange={(e) => setGuestName(e.target.value)}
                        placeholder="Enter your name or leave blank"
                        maxLength={100}
                        disabled={isUploading}
                      />
                    </div>

                    <div className="form-group-simple">
                      <label>Email (Optional)</label>
                      <input
                        type="email"
                        value={guestEmail}
                        onChange={(e) => setGuestEmail(e.target.value)}
                        placeholder="<EMAIL>"
                        disabled={isUploading}
                      />
                    </div>

                    {/* Upload Progress */}
                    {isUploading && (
                      <div className="upload-progress-form">
                        <div className="progress-bar-form">
                          <div
                            className="progress-fill-form"
                            style={{ width: `${uploadProgress}%` }}
                          />
                        </div>
                        <span>{uploadProgress}% Uploading...</span>
                      </div>
                    )}

                    {/* Submit Button */}
                    <button
                      className="submit-btn-form"
                      onClick={handleSubmit}
                      disabled={isUploading}
                    >
                      {isUploading ? 'Uploading...' : 'Add to Mosaic Wall'}
                    </button>
                  </div>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CapturePage;
