import { useState, useEffect, useCallback } from 'react';

/**
 * Hook for handling offline support and retry logic
 */
export const useOfflineSupport = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [offlineQueue, setOfflineQueue] = useState([]);
  const [retryAttempts, setRetryAttempts] = useState(new Map());

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => {
      console.log('🌐 Back online');
      setIsOnline(true);
      processOfflineQueue();
    };

    const handleOffline = () => {
      console.log('📴 Gone offline');
      setIsOnline(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Add request to offline queue
  const queueOfflineRequest = useCallback((request) => {
    const queueItem = {
      id: Date.now() + Math.random(),
      timestamp: new Date().toISOString(),
      ...request
    };

    setOfflineQueue(prev => [...prev, queueItem]);
    
    // Store in localStorage for persistence
    const stored = JSON.parse(localStorage.getItem('offlineQueue') || '[]');
    stored.push(queueItem);
    localStorage.setItem('offlineQueue', JSON.stringify(stored));

    return queueItem.id;
  }, []);

  // Remove request from offline queue
  const removeFromQueue = useCallback((id) => {
    setOfflineQueue(prev => prev.filter(item => item.id !== id));
    
    // Update localStorage
    const stored = JSON.parse(localStorage.getItem('offlineQueue') || '[]');
    const filtered = stored.filter(item => item.id !== id);
    localStorage.setItem('offlineQueue', JSON.stringify(filtered));
  }, []);

  // Process offline queue when back online
  const processOfflineQueue = useCallback(async () => {
    const queue = JSON.parse(localStorage.getItem('offlineQueue') || '[]');
    
    for (const item of queue) {
      try {
        await retryRequest(item);
        removeFromQueue(item.id);
      } catch (error) {
        console.error('Failed to process offline request:', error);
      }
    }
  }, [removeFromQueue]);

  // Retry a request with exponential backoff
  const retryRequest = useCallback(async (request, maxRetries = 3) => {
    const requestId = request.id || Date.now();
    const currentAttempts = retryAttempts.get(requestId) || 0;

    if (currentAttempts >= maxRetries) {
      throw new Error(`Max retries (${maxRetries}) exceeded for request ${requestId}`);
    }

    try {
      // Calculate delay with exponential backoff
      const delay = Math.min(1000 * Math.pow(2, currentAttempts), 30000); // Max 30 seconds
      
      if (currentAttempts > 0) {
        await new Promise(resolve => setTimeout(resolve, delay));
      }

      // Attempt the request
      const response = await fetch(request.url, {
        method: request.method || 'GET',
        headers: request.headers || {},
        body: request.body
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Success - clear retry attempts
      setRetryAttempts(prev => {
        const updated = new Map(prev);
        updated.delete(requestId);
        return updated;
      });

      return response;
    } catch (error) {
      // Update retry attempts
      setRetryAttempts(prev => {
        const updated = new Map(prev);
        updated.set(requestId, currentAttempts + 1);
        return updated;
      });

      // If we haven't exceeded max retries, throw to trigger another attempt
      if (currentAttempts + 1 < maxRetries) {
        throw error;
      }

      // Max retries exceeded
      throw new Error(`Request failed after ${maxRetries} attempts: ${error.message}`);
    }
  }, [retryAttempts]);

  // Enhanced fetch with retry logic and offline support
  const fetchWithRetry = useCallback(async (url, options = {}, retryOptions = {}) => {
    const {
      maxRetries = 3,
      retryDelay = 1000,
      retryCondition = (error) => true
    } = retryOptions;

    // If offline, queue the request
    if (!isOnline) {
      const queueId = queueOfflineRequest({
        url,
        method: options.method || 'GET',
        headers: options.headers,
        body: options.body
      });

      throw new Error(`Request queued for when online (ID: ${queueId})`);
    }

    let lastError;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        // Add delay for retry attempts
        if (attempt > 0) {
          const delay = retryDelay * Math.pow(2, attempt - 1);
          await new Promise(resolve => setTimeout(resolve, delay));
        }

        const response = await fetch(url, options);
        
        // Check if response is ok
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        return response;
      } catch (error) {
        lastError = error;
        
        // Check if we should retry this error
        if (!retryCondition(error) || attempt === maxRetries) {
          break;
        }
        
        console.warn(`Attempt ${attempt + 1} failed, retrying...`, error.message);
      }
    }

    throw lastError;
  }, [isOnline, queueOfflineRequest]);

  // Load offline queue from localStorage on mount
  useEffect(() => {
    const stored = JSON.parse(localStorage.getItem('offlineQueue') || '[]');
    setOfflineQueue(stored);
  }, []);

  // Process queue when coming back online
  useEffect(() => {
    if (isOnline && offlineQueue.length > 0) {
      processOfflineQueue();
    }
  }, [isOnline, offlineQueue.length, processOfflineQueue]);

  return {
    isOnline,
    offlineQueue,
    queueOfflineRequest,
    removeFromQueue,
    fetchWithRetry,
    retryRequest,
    processOfflineQueue
  };
};

/**
 * Hook for managing upload queue with offline support
 */
export const useUploadQueue = () => {
  const [uploadQueue, setUploadQueue] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const { isOnline, fetchWithRetry } = useOfflineSupport();

  // Add upload to queue
  const queueUpload = useCallback((file, metadata = {}) => {
    const upload = {
      id: Date.now() + Math.random(),
      file,
      metadata,
      status: 'queued',
      progress: 0,
      error: null,
      timestamp: new Date().toISOString()
    };

    setUploadQueue(prev => [...prev, upload]);
    return upload.id;
  }, []);

  // Remove upload from queue
  const removeUpload = useCallback((id) => {
    setUploadQueue(prev => prev.filter(upload => upload.id !== id));
  }, []);

  // Update upload status
  const updateUpload = useCallback((id, updates) => {
    setUploadQueue(prev => prev.map(upload => 
      upload.id === id ? { ...upload, ...updates } : upload
    ));
  }, []);

  // Process upload queue
  const processQueue = useCallback(async () => {
    if (isProcessing || !isOnline) return;

    setIsProcessing(true);

    const queuedUploads = uploadQueue.filter(upload => upload.status === 'queued');

    for (const upload of queuedUploads) {
      try {
        updateUpload(upload.id, { status: 'uploading', progress: 0 });

        // Create FormData
        const formData = new FormData();
        formData.append('photo', upload.file);
        
        Object.keys(upload.metadata).forEach(key => {
          formData.append(key, upload.metadata[key]);
        });

        // Upload with retry logic
        const response = await fetchWithRetry('/api/photos', {
          method: 'POST',
          body: formData
        }, {
          maxRetries: 3,
          retryDelay: 2000,
          retryCondition: (error) => !error.message.includes('400') // Don't retry client errors
        });

        const result = await response.json();
        
        updateUpload(upload.id, { 
          status: 'completed', 
          progress: 100,
          result 
        });

        // Remove completed upload after delay
        setTimeout(() => removeUpload(upload.id), 3000);

      } catch (error) {
        updateUpload(upload.id, { 
          status: 'failed', 
          error: error.message 
        });
      }
    }

    setIsProcessing(false);
  }, [uploadQueue, isProcessing, isOnline, fetchWithRetry, updateUpload, removeUpload]);

  // Auto-process queue when online
  useEffect(() => {
    if (isOnline && uploadQueue.some(upload => upload.status === 'queued')) {
      processQueue();
    }
  }, [isOnline, uploadQueue, processQueue]);

  return {
    uploadQueue,
    queueUpload,
    removeUpload,
    updateUpload,
    processQueue,
    isProcessing
  };
};
