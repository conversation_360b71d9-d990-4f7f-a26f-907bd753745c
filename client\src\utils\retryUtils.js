/**
 * Retry utility for handling failed operations with exponential backoff
 */

/**
 * Retry an async operation with exponential backoff
 * @param {Function} operation - The async operation to retry
 * @param {Object} options - Retry options
 * @returns {Promise} - Result of the operation
 */
export const retryWithBackoff = async (operation, options = {}) => {
  const {
    maxRetries = 3,
    baseDelay = 1000,
    maxDelay = 10000,
    backoffFactor = 2,
    onRetry = null
  } = options;

  let lastError;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const result = await operation();
      return result;
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      const delay = Math.min(
        baseDelay * Math.pow(backoffFactor, attempt),
        maxDelay
      );
      
      console.log(`⏳ Retry attempt ${attempt + 1}/${maxRetries} in ${delay}ms:`, error.message);
      
      if (onRetry) {
        onRetry(attempt + 1, error, delay);
      }
      
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
};

/**
 * Retry photo upload with specific handling for network errors
 * @param {Function} uploadFunction - The upload function to retry
 * @param {Object} options - Retry options
 * @returns {Promise} - Upload result
 */
export const retryPhotoUpload = async (uploadFunction, options = {}) => {
  const defaultOptions = {
    maxRetries: 3,
    baseDelay: 2000,
    maxDelay: 15000,
    onRetry: (attempt, error, delay) => {
      console.log(`📤 Retrying photo upload (${attempt}/3) in ${delay/1000}s...`);
    }
  };

  return retryWithBackoff(uploadFunction, { ...defaultOptions, ...options });
};

/**
 * Check if an error is retryable
 * @param {Error} error - The error to check
 * @returns {boolean} - Whether the error is retryable
 */
export const isRetryableError = (error) => {
  // Network errors
  if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {
    return true;
  }
  
  // Timeout errors
  if (error.name === 'TimeoutError' || error.message.includes('timeout')) {
    return true;
  }
  
  // Server errors (5xx)
  if (error.status >= 500 && error.status < 600) {
    return true;
  }
  
  // Rate limiting (429)
  if (error.status === 429) {
    return true;
  }
  
  // Connection errors
  if (error.message.includes('Failed to fetch') || 
      error.message.includes('ERR_NETWORK') ||
      error.message.includes('ERR_INTERNET_DISCONNECTED')) {
    return true;
  }
  
  return false;
};

/**
 * Smart retry that only retries retryable errors
 * @param {Function} operation - The operation to retry
 * @param {Object} options - Retry options
 * @returns {Promise} - Operation result
 */
export const smartRetry = async (operation, options = {}) => {
  const wrappedOperation = async () => {
    try {
      return await operation();
    } catch (error) {
      if (!isRetryableError(error)) {
        console.log('❌ Non-retryable error, failing immediately:', error.message);
        throw error;
      }
      throw error;
    }
  };

  return retryWithBackoff(wrappedOperation, options);
};

/**
 * Batch retry for multiple operations
 * @param {Array} operations - Array of operations to retry
 * @param {Object} options - Retry options
 * @returns {Promise<Array>} - Array of results
 */
export const batchRetry = async (operations, options = {}) => {
  const {
    concurrency = 3,
    failFast = false,
    ...retryOptions
  } = options;

  const results = [];
  const errors = [];
  
  // Process operations in batches
  for (let i = 0; i < operations.length; i += concurrency) {
    const batch = operations.slice(i, i + concurrency);
    
    const batchPromises = batch.map(async (operation, index) => {
      try {
        const result = await smartRetry(operation, retryOptions);
        return { success: true, result, index: i + index };
      } catch (error) {
        const errorResult = { success: false, error, index: i + index };
        if (failFast) {
          throw errorResult;
        }
        return errorResult;
      }
    });
    
    const batchResults = await Promise.all(batchPromises);
    
    batchResults.forEach(result => {
      if (result.success) {
        results[result.index] = result.result;
      } else {
        errors[result.index] = result.error;
      }
    });
  }
  
  return { results, errors };
};

export default {
  retryWithBackoff,
  retryPhotoUpload,
  isRetryableError,
  smartRetry,
  batchRetry
};
