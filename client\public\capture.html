<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Capture Photo - Mosaic Wall</title>
    <style>
        body {
            margin: 0;
            padding: 2rem;
            background: #f8f9fa;
            color: #333;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
        }

        .redirect-container {
            max-width: 400px;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
        }
        
        .camera-icon {
            font-size: 4rem;
            margin-bottom: 2rem;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        
        h1 {
            font-size: 1.8rem;
            margin-bottom: 1rem;
            color: #333;
        }

        p {
            font-size: 1rem;
            margin-bottom: 2rem;
            color: #666;
        }

        .capture-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1.1rem;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .capture-btn:hover {
            background: #0056b3;
            transform: translateY(-1px);
            color: white;
            text-decoration: none;
        }
        
        .loading {
            margin-top: 1rem;
            opacity: 0.6;
        }
    </style>
</head>
<body>
    <div class="redirect-container">
        <div class="camera-icon">📸</div>
        <h1>Capture Your Photo</h1>
        <p>Click below to open the camera and add your photo to the mosaic wall!</p>
        <a href="/capture" class="capture-btn" id="captureBtn">
            Open Camera
        </a>
        <div class="loading" id="loading" style="display: none;">
            Redirecting...
        </div>
    </div>

    <script>
        // Auto-redirect after 3 seconds
        setTimeout(() => {
            document.getElementById('loading').style.display = 'block';
            window.location.href = '/capture';
        }, 3000);
        
        // Handle manual click
        document.getElementById('captureBtn').addEventListener('click', (e) => {
            e.preventDefault();
            document.getElementById('loading').style.display = 'block';
            setTimeout(() => {
                window.location.href = '/capture';
            }, 500);
        });
    </script>
</body>
</html>
