import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import PhotoTile from './PhotoTile';
import BaseImageOverlay from './BaseImageOverlay';
import { useSocket } from '../context/SocketContext';

const PhotoGrid = ({
  onPhotoClick,
  showGuestNames = true,
  autoResize = true,
  isAdmin = false,
  showBaseImage = true,
  fallbackBaseImage = null
}) => {
  const { gridState, connected } = useSocket();
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [newPhotos, setNewPhotos] = useState(new Set());
  const [forceUpdate, setForceUpdate] = useState(0);

  // Force re-render when grid state changes
  useEffect(() => {
    if (gridState?.timestamp) {
      console.log('🔄 Grid state changed, forcing re-render');
      setForceUpdate(Date.now()); // Use timestamp for unique keys
    }
  }, [gridState?.timestamp, gridState?.stats?.totalPhotos, gridState?.lastUpdate]);

  // Calculate optimal tile size based on container and grid dimensions
  const { tileSize, gridStyle } = useMemo(() => {
    if (!gridState) {
      return { tileSize: 100, gridStyle: {} };
    }

    const { rows, cols } = gridState.gridSize;

    if (isAdmin) {
      // Admin view - use container size with padding
      if (!containerSize.width || !containerSize.height) {
        return { tileSize: 100, gridStyle: {} };
      }

      const padding = 20;
      const gap = 2;
      const availableWidth = containerSize.width - (padding * 2);
      const availableHeight = containerSize.height - (padding * 2);
      const maxTileWidth = (availableWidth - (gap * (cols - 1))) / cols;
      const maxTileHeight = (availableHeight - (gap * (rows - 1))) / rows;
      const calculatedTileSize = Math.min(maxTileWidth, maxTileHeight);
      const finalTileSize = Math.max(calculatedTileSize, 20);

      return {
        tileSize: finalTileSize,
        gridStyle: {
          display: 'grid',
          gridTemplateColumns: `repeat(${cols}, ${finalTileSize}px)`,
          gridTemplateRows: `repeat(${rows}, ${finalTileSize}px)`,
          gap: `${gap}px`,
          width: (finalTileSize * cols) + (gap * (cols - 1)),
          height: (finalTileSize * rows) + (gap * (rows - 1))
        }
      };
    } else {
      // Fullscreen view - fill entire screen with rectangular tiles
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;
      const gap = 1;

      // Calculate tile dimensions to fill entire screen completely
      const tileWidth = (viewportWidth - (gap * (cols - 1))) / cols;
      const tileHeight = (viewportHeight - (gap * (rows - 1))) / rows;

      return {
        tileSize: { width: tileWidth, height: tileHeight }, // Use object for rectangular tiles
        gridStyle: {
          display: 'grid',
          gridTemplateColumns: `repeat(${cols}, ${tileWidth}px)`,
          gridTemplateRows: `repeat(${rows}, ${tileHeight}px)`,
          gap: `${gap}px`,
          width: '100vw',
          height: '100vh',
          position: 'fixed',
          top: 0,
          left: 0
        }
      };
    }
  }, [gridState, containerSize, isAdmin]);

  // Handle container resize
  useEffect(() => {
    if (!autoResize) return;

    const updateSize = () => {
      const container = document.querySelector('.mosaic-grid-container');
      if (container) {
        const rect = container.getBoundingClientRect();
        setContainerSize({
          width: rect.width,
          height: rect.height
        });
      }
    };

    updateSize();
    window.addEventListener('resize', updateSize);
    
    return () => window.removeEventListener('resize', updateSize);
  }, [autoResize]);

  // Track new photos for animation
  useEffect(() => {
    if (!gridState?.grid) return;

    const currentPhotos = new Set();
    gridState.grid.forEach((row, rowIndex) => {
      row.forEach((photo, colIndex) => {
        if (photo) {
          const photoKey = `${photo._id}-${rowIndex}-${colIndex}`;
          currentPhotos.add(photoKey);
          
          // Check if this is a new photo
          if (!newPhotos.has(photoKey)) {
            setNewPhotos(prev => new Set([...prev, photoKey]));
            
            // Remove from new photos after animation
            setTimeout(() => {
              setNewPhotos(prev => {
                const updated = new Set(prev);
                updated.delete(photoKey);
                return updated;
              });
            }, 2000);
          }
        }
      });
    });
  }, [gridState?.grid]);

  const handlePhotoClick = (photo, row, col) => {
    if (onPhotoClick) {
      onPhotoClick(photo, row, col);
    }
  };



  if (!connected) {
    return (
      <div className="mosaic-grid-container">
        <div className="connection-status">
          <div className="loading-spinner"></div>
          <p>Connecting to mosaic wall...</p>
        </div>
      </div>
    );
  }

  if (!gridState) {
    return (
      <div className="mosaic-grid-container">
        <div className="loading-grid">
          <div className="loading-spinner"></div>
          <p>Loading mosaic grid...</p>
        </div>
      </div>
    );
  }

  const { grid, gridSize, stats } = gridState;

  return (
    <div
      style={{
        width: isAdmin ? '100%' : '100vw',
        height: isAdmin ? '100%' : '100vh',
        padding: isAdmin ? '1rem' : '0',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        position: isAdmin ? 'relative' : 'fixed',
        top: isAdmin ? 'auto' : 0,
        left: isAdmin ? 'auto' : 0,
        zIndex: isAdmin ? 'auto' : 1
      }}
    >
      <div
        style={{
          ...gridStyle,
          position: 'relative' // Enable positioning for overlay
        }}
      >
        {/* Photo Grid Layer (Bottom) */}
        {grid.map((row, rowIndex) =>
          row.map((photo, colIndex) => {
            const photoKey = photo ? `${photo._id}-${rowIndex}-${colIndex}` : null;
            const isNew = photoKey ? newPhotos.has(photoKey) : false;

            return (
              <PhotoTile
                key={`${rowIndex}-${colIndex}-${photo?._id || 'empty'}-${forceUpdate}`}
                photo={photo}
                row={rowIndex}
                col={colIndex}
                size={tileSize}
                isNew={isNew}
                onClick={handlePhotoClick}
                showGuestName={showGuestNames}
                isAdmin={isAdmin}
              />
            );
          })
        )}

        {/* Base Image Overlay Layer (Top) */}
        {showBaseImage && (
          <BaseImageOverlay
            baseImageUrl={gridState?.baseImage?.cloudinaryUrl || fallbackBaseImage?.cloudinaryUrl}
            gridRows={gridSize.rows}
            gridCols={gridSize.cols}
            tileSize={tileSize}
            gridStyle={gridStyle}
          />
        )}
      </div>
    </div>
  );
};

export default PhotoGrid;
