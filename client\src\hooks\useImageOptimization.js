import { useState, useEffect, useCallback, useRef } from 'react';

/**
 * Hook for optimizing image loading and caching
 */
export const useImageOptimization = () => {
  const [imageCache, setImageCache] = useState(new Map());
  const [loadingImages, setLoadingImages] = useState(new Set());
  const observerRef = useRef(null);

  // Initialize Intersection Observer for lazy loading
  useEffect(() => {
    if ('IntersectionObserver' in window) {
      observerRef.current = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              const img = entry.target;
              const src = img.dataset.src;
              if (src && !img.src) {
                loadImage(src, img);
              }
            }
          });
        },
        {
          rootMargin: '50px', // Start loading 50px before image comes into view
          threshold: 0.1
        }
      );
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);

  // Load and cache image
  const loadImage = useCallback((src, imgElement = null) => {
    if (imageCache.has(src)) {
      // Image already cached
      if (imgElement) {
        imgElement.src = src;
        imgElement.classList.add('loaded');
      }
      return Promise.resolve(src);
    }

    if (loadingImages.has(src)) {
      // Image already loading
      return new Promise((resolve) => {
        const checkLoaded = () => {
          if (imageCache.has(src)) {
            resolve(src);
          } else {
            setTimeout(checkLoaded, 100);
          }
        };
        checkLoaded();
      });
    }

    // Start loading image
    setLoadingImages(prev => new Set([...prev, src]));

    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        // Cache the loaded image
        setImageCache(prev => new Map([...prev, [src, img]]));
        setLoadingImages(prev => {
          const updated = new Set(prev);
          updated.delete(src);
          return updated;
        });

        // Update the actual img element if provided
        if (imgElement) {
          imgElement.src = src;
          imgElement.classList.add('loaded');
        }

        resolve(src);
      };

      img.onerror = () => {
        setLoadingImages(prev => {
          const updated = new Set(prev);
          updated.delete(src);
          return updated;
        });

        if (imgElement) {
          imgElement.classList.add('error');
        }

        reject(new Error(`Failed to load image: ${src}`));
      };

      img.src = src;
    });
  }, [imageCache, loadingImages]);

  // Preload images
  const preloadImages = useCallback((urls) => {
    const promises = urls.map(url => loadImage(url));
    return Promise.allSettled(promises);
  }, [loadImage]);

  // Lazy load image with intersection observer
  const lazyLoadImage = useCallback((imgElement, src) => {
    if (!imgElement || !src) return;

    imgElement.dataset.src = src;
    imgElement.classList.add('lazy');

    if (observerRef.current) {
      observerRef.current.observe(imgElement);
    } else {
      // Fallback for browsers without IntersectionObserver
      loadImage(src, imgElement);
    }
  }, [loadImage]);

  // NO optimization - return original image URL
  const getOptimizedImageUrl = useCallback((originalUrl, options = {}) => {
    if (!originalUrl) return '';

    // Always return original URL - NO compression or optimization
    console.log('📸 Using original image URL - NO optimization');
    return originalUrl;
  }, []);

  // Clear cache (useful for memory management)
  const clearCache = useCallback(() => {
    setImageCache(new Map());
    setLoadingImages(new Set());
  }, []);

  // Get cache statistics
  const getCacheStats = useCallback(() => {
    return {
      cachedImages: imageCache.size,
      loadingImages: loadingImages.size,
      totalMemoryUsage: Array.from(imageCache.values()).reduce((total, img) => {
        // Rough estimate of memory usage
        return total + (img.width * img.height * 4); // 4 bytes per pixel (RGBA)
      }, 0)
    };
  }, [imageCache, loadingImages]);

  return {
    loadImage,
    preloadImages,
    lazyLoadImage,
    getOptimizedImageUrl,
    clearCache,
    getCacheStats,
    isImageCached: (src) => imageCache.has(src),
    isImageLoading: (src) => loadingImages.has(src)
  };
};

/**
 * Hook for responsive image sizing
 */
export const useResponsiveImages = (containerRef) => {
  const [containerSize, setContainerSize] = useState({ width: 0, height: 0 });
  const [devicePixelRatio, setDevicePixelRatio] = useState(window.devicePixelRatio || 1);

  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setContainerSize({
          width: rect.width,
          height: rect.height
        });
      }
    };

    const updatePixelRatio = () => {
      setDevicePixelRatio(window.devicePixelRatio || 1);
    };

    updateSize();
    updatePixelRatio();

    window.addEventListener('resize', updateSize);
    window.addEventListener('orientationchange', updateSize);
    
    // Listen for pixel ratio changes (zoom, different displays)
    const mediaQuery = window.matchMedia(`(resolution: ${devicePixelRatio}dppx)`);
    mediaQuery.addListener(updatePixelRatio);

    // Use ResizeObserver if available
    let resizeObserver;
    if (window.ResizeObserver && containerRef.current) {
      resizeObserver = new ResizeObserver(updateSize);
      resizeObserver.observe(containerRef.current);
    }

    return () => {
      window.removeEventListener('resize', updateSize);
      window.removeEventListener('orientationchange', updateSize);
      mediaQuery.removeListener(updatePixelRatio);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [containerRef]);

  // Calculate optimal image dimensions
  const getOptimalImageSize = useCallback((targetWidth, targetHeight) => {
    const scale = Math.min(devicePixelRatio, 2); // Cap at 2x for performance
    return {
      width: Math.round(targetWidth * scale),
      height: Math.round(targetHeight * scale)
    };
  }, [devicePixelRatio]);

  return {
    containerSize,
    devicePixelRatio,
    getOptimalImageSize
  };
};

/**
 * Hook for progressive image loading
 */
export const useProgressiveImage = (src, placeholderSrc) => {
  const [currentSrc, setCurrentSrc] = useState(placeholderSrc);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);

  useEffect(() => {
    if (!src) return;

    setLoading(true);
    setError(false);

    const img = new Image();
    
    img.onload = () => {
      setCurrentSrc(src);
      setLoading(false);
    };

    img.onerror = () => {
      setError(true);
      setLoading(false);
    };

    img.src = src;

    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [src]);

  return { src: currentSrc, loading, error };
};
