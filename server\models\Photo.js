const mongoose = require('mongoose');

const photoSchema = new mongoose.Schema({
  // Cloudinary information
  cloudinaryUrl: {
    type: String,
    required: true,
    trim: true
  },
  cloudinaryPublicId: {
    type: String,
    required: true,
    trim: true
  },
  
  // Photo metadata
  originalName: {
    type: String,
    trim: true
  },
  fileSize: {
    type: Number
  },
  mimeType: {
    type: String,
    trim: true
  },
  
  // Guest information
  guestName: {
    type: String,
    trim: true,
    maxlength: 100
  },
  guestEmail: {
    type: String,
    trim: true,
    lowercase: true
  },
  
  // Color analysis
  colorAvg: {
    r: { type: Number, min: 0, max: 255 },
    g: { type: Number, min: 0, max: 255 },
    b: { type: Number, min: 0, max: 255 },
    hex: { type: String, match: /^#[0-9A-F]{6}$/i }
  },
  
  // Grid position
  gridRow: {
    type: Number,
    min: 0
  },
  gridCol: {
    type: Number,
    min: 0
  },
  
  // Event association
  eventId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Event',
    required: true
  },
  
  // Status and flags
  isApproved: {
    type: Boolean,
    default: true
  },
  isVisible: {
    type: Boolean,
    default: true
  },
  isProcessed: {
    type: Boolean,
    default: false
  },
  
  // Timestamps
  uploadedAt: {
    type: Date,
    default: Date.now
  },
  processedAt: {
    type: Date
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
photoSchema.index({ eventId: 1, uploadedAt: -1 });
photoSchema.index({ gridRow: 1, gridCol: 1, eventId: 1 });
photoSchema.index({ isVisible: 1, isApproved: 1 });

// Virtual for thumbnail URL
photoSchema.virtual('thumbnailUrl').get(function() {
  if (this.cloudinaryUrl) {
    // Generate Cloudinary thumbnail URL (200x200, crop to fill)
    return this.cloudinaryUrl.replace('/upload/', '/upload/w_200,h_200,c_fill/');
  }
  return null;
});

// Virtual for grid URL (optimized for grid display)
photoSchema.virtual('gridUrl').get(function() {
  if (this.cloudinaryUrl) {
    // Generate Cloudinary grid URL (300x300, crop to fill, quality auto)
    return this.cloudinaryUrl.replace('/upload/', '/upload/w_300,h_300,c_fill,q_auto/');
  }
  return null;
});

// Method to update grid position
photoSchema.methods.updateGridPosition = function(row, col) {
  this.gridRow = row;
  this.gridCol = col;
  return this.save();
};

// Method to mark as processed
photoSchema.methods.markAsProcessed = function(colorData) {
  this.isProcessed = true;
  this.processedAt = new Date();
  if (colorData) {
    this.colorAvg = colorData;
  }
  return this.save();
};

// Static method to find photos by grid position
photoSchema.statics.findByGridPosition = function(eventId, row, col) {
  return this.findOne({ eventId, gridRow: row, gridCol: col, isVisible: true });
};

// Static method to get photos for event
photoSchema.statics.findByEvent = function(eventId, options = {}) {
  const query = { eventId, isVisible: true };
  if (options.approved !== undefined) {
    query.isApproved = options.approved;
  }
  
  return this.find(query)
    .sort(options.sort || { uploadedAt: -1 })
    .limit(options.limit || 0);
};

module.exports = mongoose.model('Photo', photoSchema);
