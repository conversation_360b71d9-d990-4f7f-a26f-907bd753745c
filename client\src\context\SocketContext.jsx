import React, { createContext, useContext, useEffect, useState } from 'react';
import { io } from 'socket.io-client';

const SocketContext = createContext();

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

const SocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null);
  const [connected, setConnected] = useState(false);
  const [eventId, setEventId] = useState('default');
  const [gridState, setGridState] = useState(null);
  const [connectionStats, setConnectionStats] = useState(null);

  useEffect(() => {
    // Initialize socket connection
    const socketUrl = import.meta.env.VITE_SOCKET_URL || 'http://localhost:5000';
    console.log('🔌 Connecting to socket:', socketUrl);

    const newSocket = io(socketUrl, {
      transports: ['polling', 'websocket'], // Try polling first, then websocket
      timeout: 20000,
      forceNew: true,
      autoConnect: true,
      reconnection: true,
      reconnectionDelay: 1000,
      reconnectionAttempts: 5
    });

    // Connection event handlers
    newSocket.on('connect', () => {
      console.log('✅ Connected to server:', newSocket.id);
      setConnected(true);
      setSocket(newSocket);
      
      // Join default room
      newSocket.emit('join-room', { 
        eventId: 'default',
        userInfo: {
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString()
        }
      });
    });

    newSocket.on('disconnect', (reason) => {
      console.log('❌ Disconnected from server:', reason);
      setConnected(false);
    });

    newSocket.on('connect_error', (error) => {
      console.error('❌ Connection error:', error);
      setConnected(false);
    });

    newSocket.on('reconnect', (attemptNumber) => {
      console.log('🔄 Reconnected after', attemptNumber, 'attempts');
      setConnected(true);
    });

    newSocket.on('reconnect_error', (error) => {
      console.error('❌ Reconnection error:', error);
    });

    newSocket.on('reconnect_failed', () => {
      console.error('❌ Failed to reconnect to server');
      setConnected(false);
    });

    // Room event handlers
    newSocket.on('room-joined', (data) => {
      console.log('🏠 Joined room:', data);
      setEventId(data.eventId);

      // Request initial grid state after joining room
      setTimeout(() => {
        console.log('🔄 Requesting initial grid state...');
        newSocket.emit('request-grid-update');
      }, 500);
    });

    newSocket.on('grid-state', (data) => {
      console.log('🎯 Grid state received - updating immediately:', data);
      console.log('🎯 Grid data details:', {
        eventId: data.eventId,
        gridSize: data.gridSize,
        gridLength: data.grid?.length,
        firstRowLength: data.grid?.[0]?.length,
        photosInGrid: data.grid?.flat().filter(Boolean).length,
        stats: data.stats,
        baseImage: data.baseImage,
        hasBaseImage: !!data.baseImage?.cloudinaryUrl,
        timestamp: data.timestamp,
        immediate: data.immediate
      });

      // Force immediate state update with fresh timestamp
      setGridState({
        ...data,
        timestamp: Date.now(), // Use timestamp for forced re-renders
        lastUpdate: new Date()
      });
    });

    // Real-time photo events - immediate grid update
    newSocket.on('photo-added', (data) => {
      console.log('📸 Photo added - updating grid immediately:', data);
      // Force immediate grid refresh
      newSocket.emit('get-grid-state', { eventId: 'default' });
      // Also force immediate grid state update
      setGridState(prevState => {
        if (!prevState) {
          console.log('No previous grid state, requesting fresh state');
          newSocket.emit('request-grid-update');
          return null;
        }

        const newGrid = prevState.grid.map(row => [...row]); // Deep copy
        const { row, col } = data.gridPosition;

        if (newGrid[row] && col < newGrid[row].length) {
          newGrid[row][col] = data.photo;
          console.log(`✅ Updated grid position [${row}][${col}] with new photo`);
        }

        const updatedState = {
          ...prevState,
          grid: newGrid,
          stats: data.eventStats || {
            ...prevState.stats,
            totalPhotos: (prevState.stats?.totalPhotos || 0) + 1
          },
          timestamp: new Date()
        };

        console.log('📊 Updated grid state:', {
          totalPhotos: updatedState.stats.totalPhotos,
          gridPosition: [row, col]
        });

        return updatedState;
      });
    });

    newSocket.on('photo-deleted', (data) => {
      console.log('🗑️ Photo deleted:', data);
      // Remove photo from grid state
      setGridState(prevState => {
        if (!prevState) return null;

        const newGrid = [...prevState.grid];
        const { row, col } = data.gridPosition;

        if (newGrid[row] && newGrid[row][col] !== undefined) {
          newGrid[row][col] = null;
        }

        return {
          ...prevState,
          grid: newGrid
        };
      });
    });

    newSocket.on('all-photos-deleted', (data) => {
      console.log('🗑️ All photos deleted:', data);
      // Clear all photos from grid state
      setGridState(prevState => {
        if (!prevState) return null;

        const emptyGrid = Array(prevState.gridSize.rows).fill(null).map(() =>
          Array(prevState.gridSize.cols).fill(null)
        );

        return {
          ...prevState,
          grid: emptyGrid,
          stats: {
            totalPhotos: 0,
            fillPercentage: 0
          }
        };
      });
    });

    newSocket.on('photo-approved', (data) => {
      console.log('✅ Photo approved:', data);
      // Add approved photo to grid state
      setGridState(prevState => {
        if (!prevState) return null;

        const newGrid = [...prevState.grid];
        const { row, col } = data.gridPosition;

        if (newGrid[row] && newGrid[row][col] === undefined) {
          newGrid[row][col] = data.photo;
        }

        return {
          ...prevState,
          grid: newGrid,
          stats: {
            ...prevState.stats,
            totalPhotos: (prevState.stats?.totalPhotos || 0) + 1,
            fillPercentage: Math.round(((prevState.stats?.totalPhotos || 0) + 1) / (prevState.gridSize.rows * prevState.gridSize.cols) * 100)
          }
        };
      });
    });

    newSocket.on('grid-updated', (data) => {
      console.log('🔄 Grid updated:', data);
      // Immediately update grid state if provided, otherwise request fresh state
      if (data.gridState) {
        setGridState(data.gridState);
      } else {
        newSocket.emit('request-grid-update');
      }
    });

    // Remove duplicate listeners - they're already handled above

    newSocket.on('admin-action', (data) => {
      console.log('👨‍💼 Admin action:', data);
    });

    newSocket.on('user-joined', (data) => {
      console.log('👤 User joined:', data);
    });

    newSocket.on('user-left', (data) => {
      console.log('👤 User left:', data);
    });

    newSocket.on('error', (error) => {
      console.error('❌ Socket error:', error);
    });

    // Heartbeat
    const heartbeat = setInterval(() => {
      if (newSocket.connected) {
        newSocket.emit('ping');
      }
    }, 30000);

    newSocket.on('pong', (data) => {
      // Connection is alive
    });

    // Cleanup
    return () => {
      clearInterval(heartbeat);
      newSocket.disconnect();
    };
  }, []);

  // Helper functions
  const joinRoom = (roomId, userInfo = {}) => {
    if (socket) {
      socket.emit('join-room', { eventId: roomId, userInfo });
    }
  };

  const requestGridUpdate = () => {
    if (socket) {
      socket.emit('request-grid-update');
    }
  };

  const notifyPhotoUploading = (guestName) => {
    if (socket) {
      socket.emit('photo-uploading', { guestName });
    }
  };

  const sendAdminAction = (action, data, adminToken) => {
    if (socket) {
      socket.emit('admin-action', { action, data, adminToken });
    }
  };

  const value = {
    socket,
    connected,
    eventId,
    gridState,
    connectionStats,
    joinRoom,
    requestGridUpdate,
    notifyPhotoUploading,
    sendAdminAction
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};

export default SocketProvider;
