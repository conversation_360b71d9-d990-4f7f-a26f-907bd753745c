import axios from 'axios';
import { retryPhotoUpload, isRetryableError } from '../utils/retryUtils';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log(`🌐 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.status} ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('❌ API Response Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Photo API endpoints
export const photoAPI = {
  // Get all photos
  getPhotos: async (params = {}) => {
    const response = await api.get('/photos', { params });
    return response.data;
  },

  // Upload new photo with retry logic
  uploadPhoto: async (formData, onUploadProgress) => {
    const uploadFunction = async () => {
      try {
        const response = await api.post('/photos', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          onUploadProgress: (progressEvent) => {
            if (onUploadProgress) {
              const percentCompleted = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              );
              onUploadProgress(percentCompleted);
            }
          },
        });
        return response.data;
      } catch (error) {
        // Enhanced error handling
        if (error.response?.status === 400 && error.response?.data?.gridFull) {
          throw new Error('Grid is full - no more photos can be added');
        }

        if (error.response?.status === 429) {
          throw new Error('Too many uploads - please wait a moment');
        }

        if (isRetryableError(error)) {
          console.log('🔄 Retryable upload error:', error.message);
          throw error;
        }

        throw new Error(error.response?.data?.message || error.message || 'Upload failed');
      }
    };

    return retryPhotoUpload(uploadFunction, {
      onRetry: (attempt, error, delay) => {
        console.log(`📤 Retrying photo upload (${attempt}/3) in ${delay/1000}s: ${error.message}`);
        if (onUploadProgress) {
          onUploadProgress(0); // Reset progress on retry
        }
      }
    });
  },

  // Delete photo (admin)
  deletePhoto: async (photoId) => {
    const response = await api.delete(`/photos/${photoId}`);
    return response.data;
  },
};

// Grid API endpoints
export const gridAPI = {
  // Get grid configuration
  getGridConfig: async () => {
    const response = await api.get('/grid');
    return response.data;
  },

  // Update grid configuration (admin)
  updateGridConfig: async (config) => {
    const response = await api.put('/grid', config);
    return response.data;
  },
};

// Admin API endpoints
export const adminAPI = {
  // Admin login
  login: async (credentials) => {
    const response = await api.post('/admin/login', credentials);
    return response.data;
  },

  // Get statistics
  getStats: async () => {
    const response = await api.get('/admin/stats');
    return response.data;
  },
};

// Mosaic API endpoints
export const mosaicAPI = {
  // Get mosaic analysis
  getAnalysis: async (eventId = 'default') => {
    const response = await api.get(`/mosaic/analysis/${eventId}`);
    return response.data;
  },

  // Arrange photos by color matching
  arrangePhotos: async (eventId = 'default', targetPalette = null) => {
    const response = await api.post(`/mosaic/arrange/${eventId}`, { targetPalette });
    return response.data;
  },

  // Optimize photo placement
  optimizePhotos: async (eventId = 'default', iterations = 100) => {
    const response = await api.post(`/mosaic/optimize/${eventId}`, { iterations });
    return response.data;
  },

  // Generate color palette
  generatePalette: async (imageUrl, gridRows, gridCols) => {
    const response = await api.post('/mosaic/generate-palette', {
      imageUrl,
      gridRows,
      gridCols
    });
    return response.data;
  },

  // Get color statistics
  getColorStats: async (eventId = 'default') => {
    const response = await api.get(`/mosaic/color-stats/${eventId}`);
    return response.data;
  },

  // Shuffle photos randomly
  shufflePhotos: async (eventId = 'default') => {
    const response = await api.post(`/mosaic/shuffle/${eventId}`);
    return response.data;
  },
};

// Health check
export const healthAPI = {
  check: async () => {
    const response = await api.get('/health');
    return response.data;
  },
};

// Utility function to handle API errors
export const handleAPIError = (error) => {
  if (error.response) {
    // Server responded with error status
    const { status, data } = error.response;
    return {
      message: data.error || data.message || 'Server error occurred',
      status,
      details: data,
    };
  } else if (error.request) {
    // Request was made but no response received
    return {
      message: 'Network error - please check your connection',
      status: 0,
      details: error.request,
    };
  } else {
    // Something else happened
    return {
      message: error.message || 'An unexpected error occurred',
      status: -1,
      details: error,
    };
  }
};

// File upload helper
export const createFormData = (file, additionalData = {}) => {
  const formData = new FormData();
  formData.append('photo', file);
  
  Object.keys(additionalData).forEach(key => {
    formData.append(key, additionalData[key]);
  });
  
  return formData;
};

export default api;
