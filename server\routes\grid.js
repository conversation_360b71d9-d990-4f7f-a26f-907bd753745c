const express = require('express');
const router = express.Router();
const { Event } = require('../models');

// GET /api/grid - Get current grid configuration
router.get('/', async (req, res) => {
  try {
    const event = await Event.getDefault();

    res.json({
      success: true,
      gridConfig: {
        rows: event.gridRows,
        cols: event.gridCols,
        totalSlots: event.totalGridSlots,
        availableSlots: event.availableSlots,
        fillPercentage: event.gridFillPercentage
      },
      eventInfo: {
        id: event._id,
        name: event.name,
        description: event.description,
        isActive: event.isActive,
        allowGuestUploads: event.allowGuestUploads,
        requireGuestName: event.requireGuestName,
        maxPhotosPerGuest: event.maxPhotosPerGuest
      },
      stats: {
        totalPhotos: event.totalPhotos,
        approvedPhotos: event.approvedPhotos,
        fillPercentage: event.gridFillPercentage
      }
    });
  } catch (error) {
    console.error('Error fetching grid configuration:', error);
    res.status(500).json({
      error: 'Failed to fetch grid configuration',
      message: error.message
    });
  }
});

// PUT /api/grid - Update grid configuration (admin only)
router.put('/', async (req, res) => {
  try {
    const { rows, cols, eventSettings } = req.body;

    // Validate input
    if (rows && (rows < 1 || rows > 50)) {
      return res.status(400).json({ error: 'Rows must be between 1 and 50' });
    }

    if (cols && (cols < 1 || cols > 50)) {
      return res.status(400).json({ error: 'Columns must be between 1 and 50' });
    }

    const event = await Event.getDefault();

    // Update grid size if provided
    if (rows && cols) {
      await event.updateGridSize(rows, cols);
    }

    // Update event settings if provided
    if (eventSettings) {
      Object.keys(eventSettings).forEach(key => {
        if (event.schema.paths[key] && eventSettings[key] !== undefined) {
          event[key] = eventSettings[key];
        }
      });
      await event.save();
    }

    // Update statistics
    await event.updateStats();

    // Emit socket event for real-time updates
    const socketHandler = req.app.get('socketHandler');
    if (socketHandler) {
      socketHandler.broadcastGridUpdated(event._id, {
        gridSize: {
          rows: event.gridRows,
          cols: event.gridCols
        },
        eventSettings: {
          allowGuestUploads: event.allowGuestUploads,
          requireGuestName: event.requireGuestName,
          maxPhotosPerGuest: event.maxPhotosPerGuest
        }
      });
    }

    res.json({
      success: true,
      message: 'Grid configuration updated successfully',
      gridConfig: {
        rows: event.gridRows,
        cols: event.gridCols,
        totalSlots: event.totalGridSlots,
        fillPercentage: event.gridFillPercentage
      }
    });
  } catch (error) {
    console.error('Error updating grid configuration:', error);
    res.status(500).json({
      error: 'Failed to update grid configuration',
      message: error.message
    });
  }
});

module.exports = router;
