const mongoose = require('mongoose');

const eventSchema = new mongoose.Schema({
  // Event basic information
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    trim: true,
    maxlength: 1000
  },
  
  // Grid configuration
  gridRows: {
    type: Number,
    required: true,
    min: 1,
    max: 50,
    default: 10
  },
  gridCols: {
    type: Number,
    required: true,
    min: 1,
    max: 50,
    default: 10
  },
  
  // Event settings
  isActive: {
    type: Boolean,
    default: true
  },
  allowGuestUploads: {
    type: Boolean,
    default: true
  },
  requireGuestName: {
    type: Boolean,
    default: false
  },
  requireApproval: {
    type: Boolean,
    default: false
  },
  maxPhotosPerGuest: {
    type: Number,
    default: 5,
    min: 1,
    max: 20
  },
  
  // Mosaic configuration
  baseImage: {
    cloudinaryUrl: String,
    cloudinaryPublicId: String,
    description: String,
    uploadedAt: { type: Date, default: Date.now }
  },
  mosaicMode: {
    type: String,
    enum: ['color-match', 'simple'],
    default: 'simple'
  },
  enableColorMatching: {
    type: Boolean,
    default: false
  },

  // Grid color map for mosaic matching
  gridMap: [{
    row: { type: Number, required: true },
    col: { type: Number, required: true },
    r: { type: Number, min: 0, max: 255 },
    g: { type: Number, min: 0, max: 255 },
    b: { type: Number, min: 0, max: 255 },
    occupied: { type: Boolean, default: false },
    photoId: { type: mongoose.Schema.Types.ObjectId, ref: 'Photo' }
  }],
  
  // Event timing
  startDate: {
    type: Date,
    default: Date.now
  },
  endDate: {
    type: Date
  },
  
  // Statistics (computed fields)
  totalPhotos: {
    type: Number,
    default: 0
  },
  approvedPhotos: {
    type: Number,
    default: 0
  },
  gridFillPercentage: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  
  // Admin settings
  adminPassword: {
    type: String,
    default: 'admin123'
  },
  
  // Display settings
  theme: {
    backgroundColor: { type: String, default: '#000000' },
    textColor: { type: String, default: '#ffffff' },
    accentColor: { type: String, default: '#007bff' }
  },

  // Manual color grid for empty tiles
  manualColors: {
    type: Map,
    of: String, // Hex color values
    default: new Map()
  },
  
  // Advanced settings
  autoRefreshInterval: {
    type: Number,
    default: 5000, // 5 seconds
    min: 1000,
    max: 60000
  },
  showGuestNames: {
    type: Boolean,
    default: true
  },
  enableAnimations: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes
eventSchema.index({ isActive: 1, startDate: -1 });
eventSchema.index({ createdAt: -1 });

// Virtual for total grid slots
eventSchema.virtual('totalGridSlots').get(function() {
  return this.gridRows * this.gridCols;
});

// Virtual for available slots
eventSchema.virtual('availableSlots').get(function() {
  return this.totalGridSlots - this.totalPhotos;
});

// Virtual for grid fill percentage calculation
eventSchema.virtual('calculatedFillPercentage').get(function() {
  if (this.totalGridSlots === 0) return 0;
  return Math.round((this.totalPhotos / this.totalGridSlots) * 100);
});

// Method to update grid size
eventSchema.methods.updateGridSize = function(rows, cols) {
  this.gridRows = Math.max(1, Math.min(50, rows));
  this.gridCols = Math.max(1, Math.min(50, cols));
  return this.save();
};

// Method to update statistics
eventSchema.methods.updateStats = async function() {
  const Photo = mongoose.model('Photo');
  
  const stats = await Photo.aggregate([
    { $match: { eventId: this._id, isVisible: true } },
    {
      $group: {
        _id: null,
        totalPhotos: { $sum: 1 },
        approvedPhotos: { $sum: { $cond: ['$isApproved', 1, 0] } }
      }
    }
  ]);
  
  if (stats.length > 0) {
    this.totalPhotos = stats[0].totalPhotos;
    this.approvedPhotos = stats[0].approvedPhotos;
  } else {
    this.totalPhotos = 0;
    this.approvedPhotos = 0;
  }
  
  this.gridFillPercentage = this.calculatedFillPercentage;
  return this.save();
};

// Method to check if grid position is valid
eventSchema.methods.isValidGridPosition = function(row, col) {
  return row >= 0 && row < this.gridRows && col >= 0 && col < this.gridCols;
};

// Method to get next available grid position
eventSchema.methods.getNextAvailablePosition = async function() {
  const Photo = mongoose.model('Photo');
  
  // Get all occupied positions
  const occupiedPositions = await Photo.find(
    { eventId: this._id, isVisible: true },
    { gridRow: 1, gridCol: 1 }
  );
  
  const occupied = new Set();
  occupiedPositions.forEach(photo => {
    if (photo.gridRow !== undefined && photo.gridCol !== undefined) {
      occupied.add(`${photo.gridRow}-${photo.gridCol}`);
    }
  });
  
  // Find first available position
  for (let row = 0; row < this.gridRows; row++) {
    for (let col = 0; col < this.gridCols; col++) {
      if (!occupied.has(`${row}-${col}`)) {
        return { row, col };
      }
    }
  }
  
  return null; // Grid is full
};

// Method to set manual color for a grid position
eventSchema.methods.setManualColor = function(row, col, color) {
  const key = `${row}-${col}`;
  if (color) {
    this.manualColors.set(key, color);
  } else {
    this.manualColors.delete(key);
  }
  return this.save();
};

// Method to get manual color for a grid position
eventSchema.methods.getManualColor = function(row, col) {
  const key = `${row}-${col}`;
  return this.manualColors.get(key);
};

// Method to initialize grid map for color matching
eventSchema.methods.initializeGridMap = function() {
  try {
    this.gridMap = [];
    for (let row = 0; row < this.gridRows; row++) {
      for (let col = 0; col < this.gridCols; col++) {
        this.gridMap.push({
          row,
          col,
          r: 128, // Default gray
          g: 128,
          b: 128,
          occupied: false,
          photoId: null
        });
      }
    }
    console.log(`✅ Initialized grid map with ${this.gridMap.length} cells`);
    return this.save();
  } catch (error) {
    console.error('Error initializing grid map:', error);
    throw error;
  }
};

// Method to update grid map colors from base image
eventSchema.methods.updateGridMapColors = function(colorData) {
  try {
    if (!Array.isArray(colorData)) {
      console.error('❌ Invalid colorData provided to updateGridMapColors');
      return this;
    }

    let updatedCells = 0;
    colorData.forEach(({ row, col, r, g, b }) => {
      const cell = this.gridMap.find(c => c.row === row && c.col === col);
      if (cell) {
        cell.r = Math.max(0, Math.min(255, Math.round(r)));
        cell.g = Math.max(0, Math.min(255, Math.round(g)));
        cell.b = Math.max(0, Math.min(255, Math.round(b)));
        updatedCells++;
      }
    });

    console.log(`✅ Updated ${updatedCells} grid cells with color data`);
    return this.save();
  } catch (error) {
    console.error('Error updating grid map colors:', error);
    throw error;
  }
};

// Method to find best matching grid cell for a color
eventSchema.methods.findBestColorMatch = function(targetR, targetG, targetB) {
  if (!this.enableColorMatching || !this.gridMap.length) {
    return this.getNextAvailablePosition();
  }

  let bestMatch = null;
  let bestDistance = Infinity;
  const maxAcceptableDistance = 100; // Threshold for acceptable color match

  // Use weighted color distance formula
  this.gridMap.forEach(cell => {
    if (!cell.occupied) {
      const distance = Math.sqrt(
        0.3 * Math.pow(targetR - cell.r, 2) +
        0.59 * Math.pow(targetG - cell.g, 2) +
        0.11 * Math.pow(targetB - cell.b, 2)
      );

      if (distance < bestDistance) {
        bestDistance = distance;
        bestMatch = { row: cell.row, col: cell.col, distance };
      }
    }
  });

  // If no good color match found, fall back to next available position
  if (!bestMatch || bestDistance > maxAcceptableDistance) {
    console.log(`⚠️ No good color match found (distance: ${bestDistance?.toFixed(2)}), using next available position`);
    return this.getNextAvailablePosition();
  }

  console.log(`🎨 Color match found: distance ${bestDistance.toFixed(2)} for RGB(${targetR}, ${targetG}, ${targetB})`);
  return { row: bestMatch.row, col: bestMatch.col };
};

// Method to mark grid cell as occupied
eventSchema.methods.occupyGridCell = function(row, col, photoId) {
  const cell = this.gridMap.find(c => c.row === row && c.col === col);
  if (cell) {
    cell.occupied = true;
    cell.photoId = photoId;
  }
  return this.save();
};

// Method to free grid cell
eventSchema.methods.freeGridCell = function(row, col) {
  const cell = this.gridMap.find(c => c.row === row && c.col === col);
  if (cell) {
    cell.occupied = false;
    cell.photoId = null;
  }
  return this.save();
};

// Method to check if grid is full
eventSchema.methods.isGridFull = function() {
  if (this.enableColorMatching && this.gridMap.length > 0) {
    return this.gridMap.every(cell => cell.occupied);
  } else {
    const totalCells = this.gridRows * this.gridCols;
    return this.approvedPhotos >= totalCells;
  }
};

// Method to get grid fill statistics
eventSchema.methods.getGridFillStats = function() {
  const totalCells = this.gridRows * this.gridCols;
  let occupiedCells = 0;

  if (this.enableColorMatching && this.gridMap.length > 0) {
    occupiedCells = this.gridMap.filter(cell => cell.occupied).length;
  } else {
    occupiedCells = this.approvedPhotos;
  }

  return {
    total: totalCells,
    occupied: occupiedCells,
    available: totalCells - occupiedCells,
    percentage: totalCells > 0 ? Math.round((occupiedCells / totalCells) * 100) : 0,
    isFull: occupiedCells >= totalCells
  };
};

// Method to handle overflow when grid is full
eventSchema.methods.handleGridOverflow = async function() {
  const stats = this.getGridFillStats();

  if (stats.isFull) {
    console.log('⚠️ Grid is full, implementing overflow strategy');

    // Strategy 1: Find oldest photo and replace it
    const Photo = mongoose.model('Photo');
    const oldestPhoto = await Photo.findOne({
      eventId: this._id,
      isVisible: true,
      isApproved: true
    }).sort({ createdAt: 1 });

    if (oldestPhoto) {
      console.log(`🔄 Replacing oldest photo at [${oldestPhoto.gridRow}, ${oldestPhoto.gridCol}]`);

      // Free the grid cell
      if (this.enableColorMatching) {
        await this.freeGridCell(oldestPhoto.gridRow, oldestPhoto.gridCol);
      }

      // Mark old photo as replaced
      oldestPhoto.isVisible = false;
      oldestPhoto.replacedAt = new Date();
      await oldestPhoto.save();

      return { row: oldestPhoto.gridRow, col: oldestPhoto.gridCol };
    }
  }

  return null;
};

// Static method to get default event
eventSchema.statics.getDefault = async function() {
  let event = await this.findOne({ isActive: true }).sort({ createdAt: -1 });
  
  if (!event) {
    // Create default event if none exists
    event = await this.create({
      name: 'Default Mosaic Event',
      description: 'Default event for mosaic wall',
      gridRows: parseInt(process.env.DEFAULT_GRID_ROWS) || 10,
      gridCols: parseInt(process.env.DEFAULT_GRID_COLS) || 10
    });
  }
  
  return event;
};

module.exports = mongoose.model('Event', eventSchema);
