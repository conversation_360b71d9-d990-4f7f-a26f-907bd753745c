const { Photo, Event } = require('../models');
const { extractAverageColor } = require('../config/cloudinary');

class MosaicService {
  constructor() {
    this.colorCache = new Map();
  }

  /**
   * Calculate color distance using Euclidean distance in RGB space
   */
  colorDistance(color1, color2) {
    const dr = color1.r - color2.r;
    const dg = color1.g - color2.g;
    const db = color1.b - color2.b;
    return Math.sqrt(dr * dr + dg * dg + db * db);
  }

  /**
   * Calculate perceptual color distance using Delta E CIE76
   */
  colorDistanceDeltaE(color1, color2) {
    // Convert RGB to LAB for better perceptual distance
    const lab1 = this.rgbToLab(color1);
    const lab2 = this.rgbToLab(color2);
    
    const dl = lab1.l - lab2.l;
    const da = lab1.a - lab2.a;
    const db = lab1.b - lab2.b;
    
    return Math.sqrt(dl * dl + da * da + db * db);
  }

  /**
   * Convert RGB to LAB color space
   */
  rgbToLab(rgb) {
    // First convert RGB to XYZ
    let r = rgb.r / 255;
    let g = rgb.g / 255;
    let b = rgb.b / 255;
    
    // Apply gamma correction
    r = r > 0.04045 ? Math.pow((r + 0.055) / 1.055, 2.4) : r / 12.92;
    g = g > 0.04045 ? Math.pow((g + 0.055) / 1.055, 2.4) : g / 12.92;
    b = b > 0.04045 ? Math.pow((b + 0.055) / 1.055, 2.4) : b / 12.92;
    
    // Convert to XYZ using sRGB matrix
    let x = r * 0.4124564 + g * 0.3575761 + b * 0.1804375;
    let y = r * 0.2126729 + g * 0.7151522 + b * 0.0721750;
    let z = r * 0.0193339 + g * 0.1191920 + b * 0.9503041;
    
    // Normalize for D65 illuminant
    x = x / 0.95047;
    y = y / 1.00000;
    z = z / 1.08883;
    
    // Convert XYZ to LAB
    x = x > 0.008856 ? Math.pow(x, 1/3) : (7.787 * x + 16/116);
    y = y > 0.008856 ? Math.pow(y, 1/3) : (7.787 * y + 16/116);
    z = z > 0.008856 ? Math.pow(z, 1/3) : (7.787 * z + 16/116);
    
    const l = 116 * y - 16;
    const a = 500 * (x - y);
    const bLab = 200 * (y - z);
    
    return { l, a, b: bLab };
  }

  /**
   * Find the best matching photo for a target color
   */
  findBestColorMatch(targetColor, photos, usedPhotos = new Set()) {
    let bestMatch = null;
    let bestDistance = Infinity;
    
    for (const photo of photos) {
      if (!photo.colorAvg || usedPhotos.has(photo._id.toString())) {
        continue;
      }
      
      const distance = this.colorDistanceDeltaE(targetColor, photo.colorAvg);
      if (distance < bestDistance) {
        bestDistance = distance;
        bestMatch = photo;
      }
    }
    
    return bestMatch;
  }

  /**
   * Generate target colors from a base image for mosaic creation
   */
  async generateTargetPalette(imageUrl, gridRows, gridCols) {
    try {
      // For now, generate a gradient or pattern
      // In a full implementation, you would analyze the target image
      const palette = [];
      
      for (let row = 0; row < gridRows; row++) {
        palette[row] = [];
        for (let col = 0; col < gridCols; col++) {
          // Generate a gradient from top-left to bottom-right
          const rProgress = row / (gridRows - 1);
          const cProgress = col / (gridCols - 1);
          
          const r = Math.round(50 + (rProgress * 150));
          const g = Math.round(100 + (cProgress * 100));
          const b = Math.round(200 - (rProgress * 100));
          
          const hex = '#' + [r, g, b].map(x => {
            const hex = x.toString(16);
            return hex.length === 1 ? '0' + hex : hex;
          }).join('');
          
          palette[row][col] = { r, g, b, hex };
        }
      }
      
      return palette;
    } catch (error) {
      console.error('Error generating target palette:', error);
      throw error;
    }
  }

  /**
   * Arrange photos in grid based on color matching
   */
  async arrangePhotosInGrid(eventId, targetPalette = null) {
    try {
      const event = await Event.findById(eventId);
      if (!event) {
        throw new Error('Event not found');
      }

      // Get all available photos
      const photos = await Photo.find({
        eventId,
        isVisible: true,
        isApproved: true,
        colorAvg: { $exists: true }
      });

      if (photos.length === 0) {
        return { success: true, message: 'No photos to arrange' };
      }

      // Generate target palette if not provided
      if (!targetPalette) {
        targetPalette = await this.generateTargetPalette(
          null, 
          event.gridRows, 
          event.gridCols
        );
      }

      const usedPhotos = new Set();
      const arrangements = [];

      // Arrange photos based on color matching
      for (let row = 0; row < event.gridRows; row++) {
        for (let col = 0; col < event.gridCols; col++) {
          const targetColor = targetPalette[row][col];
          const bestMatch = this.findBestColorMatch(targetColor, photos, usedPhotos);
          
          if (bestMatch) {
            usedPhotos.add(bestMatch._id.toString());
            
            // Update photo position
            await Photo.findByIdAndUpdate(bestMatch._id, {
              gridRow: row,
              gridCol: col
            });
            
            arrangements.push({
              photoId: bestMatch._id,
              row,
              col,
              targetColor,
              photoColor: bestMatch.colorAvg,
              distance: this.colorDistanceDeltaE(targetColor, bestMatch.colorAvg)
            });
          }
        }
      }

      // Update event statistics
      await event.updateStats();

      return {
        success: true,
        arrangements,
        totalArranged: arrangements.length,
        totalPhotos: photos.length
      };
    } catch (error) {
      console.error('Error arranging photos in grid:', error);
      throw error;
    }
  }

  /**
   * Optimize photo placement using a simple genetic algorithm
   */
  async optimizePhotoPlacement(eventId, iterations = 100) {
    try {
      const event = await Event.findById(eventId);
      if (!event) {
        throw new Error('Event not found');
      }

      const photos = await Photo.find({
        eventId,
        isVisible: true,
        isApproved: true,
        colorAvg: { $exists: true }
      });

      if (photos.length === 0) {
        return { success: true, message: 'No photos to optimize' };
      }

      // Generate target palette
      const targetPalette = await this.generateTargetPalette(
        null, 
        event.gridRows, 
        event.gridCols
      );

      let bestArrangement = null;
      let bestScore = Infinity;

      // Simple optimization: try random arrangements and keep the best
      for (let i = 0; i < iterations; i++) {
        const shuffledPhotos = [...photos].sort(() => Math.random() - 0.5);
        let score = 0;
        const arrangement = [];

        let photoIndex = 0;
        for (let row = 0; row < event.gridRows; row++) {
          for (let col = 0; col < event.gridCols; col++) {
            if (photoIndex < shuffledPhotos.length) {
              const photo = shuffledPhotos[photoIndex];
              const targetColor = targetPalette[row][col];
              const distance = this.colorDistanceDeltaE(targetColor, photo.colorAvg);
              
              score += distance;
              arrangement.push({
                photoId: photo._id,
                row,
                col,
                distance
              });
              
              photoIndex++;
            }
          }
        }

        if (score < bestScore) {
          bestScore = score;
          bestArrangement = arrangement;
        }
      }

      // Apply the best arrangement
      if (bestArrangement) {
        for (const item of bestArrangement) {
          await Photo.findByIdAndUpdate(item.photoId, {
            gridRow: item.row,
            gridCol: item.col
          });
        }
      }

      // Update event statistics
      await event.updateStats();

      return {
        success: true,
        optimizationScore: bestScore,
        iterations,
        totalPhotos: photos.length
      };
    } catch (error) {
      console.error('Error optimizing photo placement:', error);
      throw error;
    }
  }

  /**
   * Get mosaic statistics and analysis
   */
  async getMosaicAnalysis(eventId) {
    try {
      const event = await Event.findById(eventId);
      if (!event) {
        throw new Error('Event not found');
      }

      const photos = await Photo.find({
        eventId,
        isVisible: true,
        isApproved: true,
        colorAvg: { $exists: true }
      });

      // Analyze color distribution
      const colorStats = {
        totalPhotos: photos.length,
        averageColors: {
          r: 0,
          g: 0,
          b: 0
        },
        colorRanges: {
          red: { min: 255, max: 0 },
          green: { min: 255, max: 0 },
          blue: { min: 255, max: 0 }
        }
      };

      if (photos.length > 0) {
        let totalR = 0, totalG = 0, totalB = 0;

        photos.forEach(photo => {
          if (photo.colorAvg) {
            totalR += photo.colorAvg.r;
            totalG += photo.colorAvg.g;
            totalB += photo.colorAvg.b;

            // Update ranges
            colorStats.colorRanges.red.min = Math.min(colorStats.colorRanges.red.min, photo.colorAvg.r);
            colorStats.colorRanges.red.max = Math.max(colorStats.colorRanges.red.max, photo.colorAvg.r);
            colorStats.colorRanges.green.min = Math.min(colorStats.colorRanges.green.min, photo.colorAvg.g);
            colorStats.colorRanges.green.max = Math.max(colorStats.colorRanges.green.max, photo.colorAvg.g);
            colorStats.colorRanges.blue.min = Math.min(colorStats.colorRanges.blue.min, photo.colorAvg.b);
            colorStats.colorRanges.blue.max = Math.max(colorStats.colorRanges.blue.max, photo.colorAvg.b);
          }
        });

        colorStats.averageColors.r = Math.round(totalR / photos.length);
        colorStats.averageColors.g = Math.round(totalG / photos.length);
        colorStats.averageColors.b = Math.round(totalB / photos.length);
      }

      return {
        success: true,
        eventInfo: {
          id: event._id,
          gridSize: {
            rows: event.gridRows,
            cols: event.gridCols,
            total: event.totalGridSlots
          },
          fillPercentage: event.gridFillPercentage
        },
        colorStats
      };
    } catch (error) {
      console.error('Error getting mosaic analysis:', error);
      throw error;
    }
  }
}

module.exports = new MosaicService();
