/**
 * NO COMPRESSION - Return original file with WebP conversion only
 * @param {File} file - Original image file
 * @param {Object} options - Not used
 * @returns {Promise<File>} - Original file or WebP conversion
 */
export const compressImage = async (file) => {
  try {
    // Check if browser supports WebP
    const supportsWebP = await checkWebPSupport();

    if (supportsWebP && file.type !== 'image/webp') {
      console.log('📸 Converting to WebP format (no compression)');
      return await convertToWebP(file);
    } else {
      console.log('📸 Using original file - NO compression');
      return file;
    }
  } catch (error) {
    console.error('WebP conversion failed, using original:', error);
    return file; // Always fallback to original file
  }
};

/**
 * Convert canvas to WebP or high quality image file
 * @param {HTMLCanvasElement} canvas - Canvas element
 * @param {Object} options - Conversion options
 * @returns {Promise<File>} - High quality image file
 */
export const compressCanvasImage = async (canvas, options = {}) => {
  const supportsWebP = await checkWebPSupport();

  return new Promise((resolve) => {
    if (supportsWebP) {
      // Use WebP with maximum quality (no compression)
      canvas.toBlob((blob) => {
        const file = new File([blob], `capture_${Date.now()}.webp`, {
          type: 'image/webp',
          lastModified: Date.now()
        });
        console.log('📸 Maximum quality WebP image created (no compression)');
        resolve(file);
      }, 'image/webp', 1.0); // 100% quality - NO compression
    } else {
      // Fallback to JPEG with maximum quality
      canvas.toBlob((blob) => {
        const file = new File([blob], `capture_${Date.now()}.jpg`, {
          type: 'image/jpeg',
          lastModified: Date.now()
        });
        console.log('📸 Maximum quality JPEG image created (WebP fallback)');
        resolve(file);
      }, 'image/jpeg', 1.0); // 100% quality JPEG
    }
  });
};

/**
 * Get optimal compression settings based on file size
 * @param {number} fileSize - File size in bytes
 * @returns {Object} - Optimal compression options
 */
export const getOptimalCompressionOptions = () => {
  console.log('📸 NO compression options - using original file');
  return {}; // No compression options - use original file
};

/**
 * Check if browser supports WebP format
 * @returns {Promise<boolean>} - True if WebP is supported
 */
const checkWebPSupport = () => {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
};

/**
 * Convert image to WebP format
 * @param {File} file - Original image file
 * @returns {Promise<File>} - WebP converted file
 */
const convertToWebP = (file) => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      canvas.width = img.width;
      canvas.height = img.height;
      ctx.drawImage(img, 0, 0);

      // Convert to WebP with maximum quality (no compression)
      canvas.toBlob((blob) => {
        if (blob) {
          const webpFile = new File([blob], file.name.replace(/\.[^/.]+$/, '.webp'), {
            type: 'image/webp',
            lastModified: Date.now()
          });
          console.log('✅ Converted to WebP (no compression):', {
            original: (file.size / 1024 / 1024).toFixed(2) + ' MB',
            webp: (webpFile.size / 1024 / 1024).toFixed(2) + ' MB'
          });
          resolve(webpFile);
        } else {
          reject(new Error('WebP conversion failed'));
        }
      }, 'image/webp', 1.0); // 100% quality - NO compression
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
};
