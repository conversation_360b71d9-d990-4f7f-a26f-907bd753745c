/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #000;
  color: #fff;
  overflow: hidden;
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
}

#root {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
}

/* Mosaic Grid Styles */
.mosaic-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  position: relative;
  overflow: hidden;
}

.mosaic-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.mosaic-header {
  padding: 1rem;
  text-align: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.mosaic-title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
  background: linear-gradient(45deg, #fff, #ccc);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.mosaic-stats {
  font-size: 0.9rem;
  opacity: 0.8;
}

.mosaic-grid-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.mosaic-grid {
  display: grid;
  background: transparent;
}

.photo-tile {
  background: #222;
  overflow: hidden;
  position: relative;
  border: 1px solid #333;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.photo-tile:hover {
  transform: scale(1.05);
  z-index: 10;
  box-shadow: 0 5px 20px rgba(255, 255, 255, 0.3);
}

.photo-tile img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  /* Remove transitions for faster display */
}

.photo-tile.loading {
  background: linear-gradient(45deg, #333, #555, #333);
  background-size: 200% 200%;
  animation: shimmer 2s infinite;
}

.photo-tile.empty {
  background:
    linear-gradient(45deg, rgba(255,255,255,0.02) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(255,255,255,0.02) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(255,255,255,0.02) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(255,255,255,0.02) 75%),
    radial-gradient(circle, #2a2a3e 0%, #1a1a2e 100%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  border: 1px solid rgba(255,255,255,0.1);
  position: relative;
}

.photo-tile.empty::before {
  content: '📷';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 1.5rem;
  opacity: 0.3;
  filter: grayscale(1);
}

.photo-tile.new-photo {
  animation: photoAppear 0.8s ease-out;
}

@keyframes shimmer {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes photoAppear {
  0% {
    transform: scale(0) rotate(180deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(90deg);
    opacity: 0.7;
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
  }
}

/* Upload Interface */
.upload-interface {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
}

.upload-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(45deg, #007bff, #0056b3);
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.6);
}

.upload-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.upload-modal-content {
  background: #222;
  padding: 2rem;
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

/* Fullscreen capture mode */
.upload-modal-content.fullscreen-capture {
  max-width: 100vw;
  width: 100vw;
  height: 100vh;
  border-radius: 0;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.fullscreen-capture .webcam-container {
  width: 100%;
  height: 70vh;
  position: relative;
}

.fullscreen-capture .webcam {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.fullscreen-capture .capture-btn {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  padding: 1rem 2rem;
  font-size: 1.2rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 50px;
  cursor: pointer;
  z-index: 10;
  box-shadow: 0 4px 20px rgba(0, 123, 255, 0.4);
}

.fullscreen-capture .capture-btn:hover {
  background: #0056b3;
  transform: translateX(-50%) scale(1.05);
}

/* Floating capture link */
.floating-capture-link {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;
}

.capture-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  text-decoration: none;
  border-radius: 50%;
  font-size: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 123, 255, 0.4);
  transition: all 0.3s ease;
}

.capture-link:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(0, 123, 255, 0.6);
  color: white;
  text-decoration: none;
}

/* Fullscreen capture form styling */
.fullscreen-capture .upload-form {
  margin-top: 2rem;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.fullscreen-capture .button-group {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
}

.fullscreen-capture .btn {
  padding: 1rem 2rem;
  font-size: 1.1rem;
  border-radius: 50px;
  min-width: 120px;
}

/* Capture Page Form Styles */
.capture-page-form {
  min-height: 100vh;
  background: #f8f9fa;
  color: #333;
  padding: 2rem;
  overflow-y: auto;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
}

.capture-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.back-btn-simple {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.back-btn-simple:hover {
  background: #5a6268;
}

.capture-header h1 {
  font-size: 1.8rem;
  margin: 0;
  color: #333;
}

.capture-form-container {
  max-width: 600px;
  margin: 0 auto;
}

.capture-card {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

/* Camera Form Section */
.camera-form-section {
  text-align: center;
}

.camera-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.camera-header h2 {
  font-size: 1.5rem;
  margin: 0;
  color: #333;
}

.camera-controls {
  display: flex;
  gap: 0.5rem;
}

.camera-switch-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.camera-switch-btn:hover {
  background: #5a6268;
}

.webcam-form-container {
  margin-bottom: 1.5rem;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid #e9ecef;
}

.webcam-form {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.capture-btn-form {
  background: #007bff;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
  max-width: 200px;
}

.capture-btn-form:hover:not(:disabled) {
  background: #0056b3;
}

.capture-btn-form:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Form Section */
.form-section h2 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: #28a745;
  text-align: center;
}

.photo-preview-form {
  text-align: center;
  margin-bottom: 2rem;
}

.preview-img-form {
  max-width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  border: 2px solid #e9ecef;
  margin-bottom: 1rem;
}

.retake-btn-form {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retake-btn-form:hover:not(:disabled) {
  background: #5a6268;
}

.error-message-form {
  background: #f8d7da;
  color: #721c24;
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  border: 1px solid #f5c6cb;
}

.form-fields {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group-simple {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group-simple label {
  font-weight: 500;
  color: #495057;
  font-size: 0.9rem;
}

.form-group-simple input {
  padding: 0.75rem;
  border: 1px solid #ced4da;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-group-simple input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group-simple input:disabled {
  background-color: #e9ecef;
  opacity: 0.6;
}

.upload-progress-form {
  text-align: center;
  margin: 1rem 0;
}

.progress-bar-form {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill-form {
  height: 100%;
  background: #007bff;
  transition: width 0.3s ease;
}

.submit-btn-form {
  background: #28a745;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
}

.submit-btn-form:hover:not(:disabled) {
  background: #218838;
  transform: translateY(-1px);
}

.submit-btn-form:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Success Message */
.success-message-form {
  text-align: center;
  padding: 2rem 1rem;
}

.success-icon-form {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.success-message-form h2 {
  color: #28a745;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.success-message-form p {
  color: #666;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.take-another-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.take-another-btn:hover {
  background: #0056b3;
  transform: translateY(-1px);
}

/* Success Page */
.success-page {
  display: flex;
  align-items: center;
  justify-content: center;
}

.success-content {
  text-align: center;
  max-width: 500px;
  padding: 3rem;
}

.success-icon {
  font-size: 4rem;
  margin-bottom: 2rem;
}

.success-content h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  color: #28a745;
}

.success-content p {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  opacity: 0.8;
}

/* Mobile Responsive for Form */
@media (max-width: 768px) {
  .capture-page-form {
    padding: 1rem;
  }

  .capture-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .capture-header h1 {
    font-size: 1.5rem;
  }

  .capture-card {
    padding: 1.5rem;
  }

  .camera-header {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .camera-header h2 {
    font-size: 1.3rem;
  }

  .camera-switch-btn {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .webcam-form {
    height: 250px;
  }

  .preview-img-form {
    height: 150px;
  }

  .capture-btn-form,
  .submit-btn-form,
  .take-another-btn {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }

  .success-message-form {
    padding: 1.5rem 0.5rem;
  }

  .success-icon-form {
    font-size: 2.5rem;
  }
}

.upload-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: #ccc;
}

.form-input {
  padding: 0.75rem;
  border: 1px solid #555;
  border-radius: 6px;
  background: #333;
  color: #fff;
  font-size: 1rem;
}

.form-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.2);
}

.file-input-wrapper {
  position: relative;
  overflow: hidden;
  display: inline-block;
  width: 100%;
}

.file-input {
  position: absolute;
  left: -9999px;
}

.file-input-label {
  display: block;
  padding: 0.75rem;
  border: 2px dashed #555;
  border-radius: 6px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #333;
}

.file-input-label:hover {
  border-color: #007bff;
  background: #444;
}

.button-group {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
}

.btn-primary {
  background: linear-gradient(45deg, #007bff, #0056b3);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);
}

.btn-secondary {
  background: #555;
  color: white;
}

.btn-secondary:hover {
  background: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
  .mosaic-title {
    font-size: 1.5rem;
  }
  
  .upload-interface {
    bottom: 1rem;
    right: 1rem;
  }
  
  .upload-button {
    width: 50px;
    height: 50px;
    font-size: 1.2rem;
  }
  
  .upload-modal-content {
    margin: 1rem;
    padding: 1.5rem;
  }
}

/* Loading States */
.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Additional component styles */
.photo-tile-wrapper {
  position: relative;
}

.empty-tile-content,
.error-tile-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  opacity: 0.5;
}

.empty-tile-icon,
.error-tile-icon {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.error-tile-text {
  font-size: 0.7rem;
  text-align: center;
}

.loading-shimmer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(45deg, #333, #555, #333);
  background-size: 200% 200%;
  animation: shimmer 2s infinite;
}

.shimmer-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.guest-name-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.8));
  color: white;
  padding: 0.5rem;
  font-size: 0.7rem;
  text-align: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.photo-tile:hover .guest-name-overlay {
  opacity: 1;
}

.upload-timestamp {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  background: rgba(0,0,0,0.7);
  color: white;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-size: 0.6rem;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.photo-tile:hover .upload-timestamp {
  opacity: 1;
}

.connection-status,
.loading-grid {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #ccc;
}

.grid-stats {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
  justify-content: center;
  font-size: 0.9rem;
}

.stat-item {
  display: flex;
  gap: 0.5rem;
}

.stat-label {
  color: #ccc;
}

.stat-value {
  color: #fff;
  font-weight: bold;
}

.mode-selection {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.mode-btn {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #555;
  background: #333;
  color: #fff;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mode-btn.active {
  background: #007bff;
  border-color: #007bff;
}

.mode-btn:hover {
  background: #444;
}

.mode-btn.active:hover {
  background: #0056b3;
}

.webcam-container {
  position: relative;
  margin-bottom: 1rem;
}

.webcam {
  width: 100%;
  max-width: 400px;
  border-radius: 8px;
}

.capture-btn,
.retake-btn {
  margin-top: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
}

.capture-btn:hover,
.retake-btn:hover {
  background: #0056b3;
}

.captured-photo,
.photo-preview {
  text-align: center;
  margin-bottom: 1rem;
}

.preview-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  object-fit: cover;
}

.upload-progress {
  margin: 1rem 0;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #333;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(45deg, #007bff, #0056b3);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.9rem;
  color: #ccc;
}

/* Full Screen Mosaic Styles */
.fullscreen-mosaic {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  z-index: 1000;
}

.fullscreen-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.fullscreen-grid {
  position: absolute;
}

.fullscreen-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
}

.loading-content {
  text-align: center;
}

.loading-content .loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-top: 4px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.loading-content p {
  font-size: 1.2rem;
  opacity: 0.8;
}

/* Connection Status */
.connection-status {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(0, 0, 0, 0.7);
  padding: 8px 16px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #ff4444;
  animation: pulse 2s infinite;
}

.status-indicator.connected .status-dot {
  background: #44ff44;
  animation: pulse-green 2s infinite;
}

.status-text {
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Grid Stats */
.grid-stats {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1001;
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(0, 0, 0, 0.7);
  padding: 8px 20px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.9rem;
  font-weight: 500;
}

/* Enhanced Photo Tile for Fullscreen */
.photo-tile.fullscreen {
  border: 1px solid rgba(255, 255, 255, 0.05);
  border-radius: 1px;
  transition: all 0.3s ease;
  box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.2);
}

.photo-tile.fullscreen.empty {
  background:
    linear-gradient(45deg, rgba(255,255,255,0.02) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(255,255,255,0.02) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(255,255,255,0.02) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(255,255,255,0.02) 75%),
    radial-gradient(circle, #2a2a3e 0%, #1a1a2e 100%);
  background-size: 8px 8px;
  background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.photo-tile.fullscreen.empty::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30%;
  height: 30%;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 50%;
}

/* Manual color tiles */
.photo-tile.manual-color {
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.photo-tile.manual-color .empty-tile-content {
  opacity: 0.8;
}

/* Admin controls for color picker */
.admin-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.photo-tile:hover .admin-controls {
  opacity: 1;
}

.color-picker {
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  background: none;
  padding: 0;
}

.color-picker::-webkit-color-swatch-wrapper {
  padding: 0;
  border-radius: 50%;
}

.color-picker::-webkit-color-swatch {
  border: 2px solid rgba(255, 255, 255, 0.5);
  border-radius: 50%;
}

.remove-color-btn {
  background: rgba(255, 0, 0, 0.8);
  color: white;
  border: none;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s ease;
}

.remove-color-btn:hover {
  background: rgba(255, 0, 0, 1);
}

/* Pulse animations */
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 68, 68, 0);
  }
}

@keyframes pulse-green {
  0% {
    box-shadow: 0 0 0 0 rgba(68, 255, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(68, 255, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(68, 255, 68, 0);
  }
}

/* Main Wall Styles */
.main-wall {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  z-index: 1000;
}

.main-wall-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.main-wall-grid {
  position: absolute;
}

.main-wall-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
}

/* Capture Button */
.capture-button-container {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1001;
}

.capture-button {
  background: linear-gradient(45deg, #007bff, #0056b3);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 123, 255, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.capture-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 25px rgba(0, 123, 255, 0.4);
  background: linear-gradient(45deg, #0056b3, #004085);
}

.capture-button:active {
  transform: translateY(0);
}

/* Capture Modal */
.capture-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(5px);
}

.capture-modal-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Notifications */
.notification {
  position: fixed;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  padding: 15px 25px;
  border-radius: 25px;
  font-weight: 500;
  z-index: 2001;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  max-width: 80vw;
  text-align: center;
}

.notification.success {
  background: rgba(40, 167, 69, 0.9);
  color: white;
  border-color: rgba(40, 167, 69, 0.3);
}

.notification.error {
  background: rgba(220, 53, 69, 0.9);
  color: white;
  border-color: rgba(220, 53, 69, 0.3);
}

/* Responsive Design for Main Wall */
@media (max-width: 768px) {
  .capture-button {
    padding: 12px 24px;
    font-size: 1rem;
  }

  .capture-button-container {
    bottom: 20px;
  }

  .capture-modal-content {
    margin: 20px;
    padding: 15px;
  }

  .notification {
    top: 20px;
    padding: 12px 20px;
    font-size: 0.9rem;
  }
}
