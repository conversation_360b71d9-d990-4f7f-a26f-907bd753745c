
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import ReactWindowMosaicWall from './components/ReactWindowMosaicWall';
import AdminDashboard from './components/AdminDashboard';
import CapturePage from './pages/CapturePage';
import SocketProvider from './context/SocketContext';
import './App.css';
import './styles/Admin.css';

function App() {
  return (
    <SocketProvider>
      <Router
        future={{
          v7_startTransition: true,
          v7_relativeSplatPath: true
        }}
      >
        <div className="App">
          <Routes>
            <Route path="/" element={<ReactWindowMosaicWall />} />
            <Route path="/capture" element={<CapturePage />} />
            <Route path="/admin" element={<AdminDashboard />} />
          </Routes>
        </div>
      </Router>
    </SocketProvider>
  );
}

export default App;
