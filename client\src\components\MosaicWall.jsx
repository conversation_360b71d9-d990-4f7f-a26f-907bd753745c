import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import PhotoGrid from './PhotoGrid';
import { useSocket } from '../context/SocketContext';

const MosaicWall = () => {
  const { connected, gridState, requestGridUpdate } = useSocket();
  const [notification, setNotification] = useState(null);
  const [selectedPhoto, setSelectedPhoto] = useState(null);
  const [showBaseImage, setShowBaseImage] = useState(true);
  const [baseImageData, setBaseImageData] = useState(null);

  // Show notifications for upload success/error
  const showNotification = (message, type = 'success') => {
    setNotification({ message, type });
    setTimeout(() => setNotification(null), 5000);
  };

  const handleUploadSuccess = (result) => {
    showNotification(`Photo uploaded successfully! Added to position (${result.gridPosition.row + 1}, ${result.gridPosition.col + 1})`, 'success');
  };

  const handleUploadError = (error) => {
    showNotification(error.message, 'error');
  };

  const handlePhotoClick = (photo, row, col) => {
    setSelectedPhoto({ photo, row, col });
  };

  const closePhotoModal = () => {
    setSelectedPhoto(null);
  };

  // Calculate fill percentage for display
  const fillPercentage = gridState?.stats?.fillPercentage || 0;
  const totalPhotos = gridState?.stats?.totalPhotos || 0;
  const gridSize = gridState?.gridSize;

  // Fetch base image data directly from API as fallback
  useEffect(() => {
    const fetchBaseImage = async () => {
      try {
        const response = await fetch('/api/mosaic/status');
        const data = await response.json();
        if (data.event?.baseImage) {
          setBaseImageData(data.event.baseImage);
        }
      } catch (error) {
        console.error('Failed to fetch base image:', error);
      }
    };

    fetchBaseImage();
  }, []);

  return (
    <div className="mosaic-container">
      {/* Main Grid - Full Screen */}
      <main className="mosaic-main">
        <PhotoGrid
          onPhotoClick={handlePhotoClick}
          showGuestNames={false}
          autoResize={false}
          isAdmin={false}
          showBaseImage={showBaseImage}
          fallbackBaseImage={baseImageData}
        />


      </main>

      {/* Floating Controls */}
      <div style={{ position: 'fixed', bottom: '2rem', right: '2rem', zIndex: 1000, display: 'flex', flexDirection: 'column', gap: '1rem' }}>


        {/* Base Image Toggle */}
        {(gridState?.baseImage || baseImageData) && (
          <motion.button
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.5 }}
            onClick={() => setShowBaseImage(!showBaseImage)}
            style={{
              width: '60px',
              height: '60px',
              borderRadius: '50%',
              border: 'none',
              background: showBaseImage ? '#28a745' : '#666',
              color: 'white',
              fontSize: '24px',
              cursor: 'pointer',
              boxShadow: '0 4px 15px rgba(0,0,0,0.3)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'all 0.3s ease'
            }}
            title={showBaseImage ? 'Hide Base Image' : 'Show Base Image'}
          >
            🖼️
          </motion.button>
        )}

        {/* Capture Link */}
        <motion.div
          className="floating-capture-link"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 1 }}
        >
          <Link
            to="/capture"
            className="capture-link"
          >
            📸
          </Link>
        </motion.div>
      </div>

      {/* Notifications */}
      {notification && (
        <motion.div
          className={`notification ${notification.type}`}
          initial={{ opacity: 0, x: 300 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: 300 }}
          style={{
            position: 'fixed',
            top: '2rem',
            right: '2rem',
            zIndex: 1000,
            padding: '1rem',
            borderRadius: '8px',
            background: notification.type === 'success' ? '#28a745' : '#dc3545',
            color: 'white',
            maxWidth: '300px',
            boxShadow: '0 4px 15px rgba(0,0,0,0.3)'
          }}
        >
          {notification.message}
        </motion.div>
      )}

      {/* Photo Detail Modal */}
      {selectedPhoto && (
        <motion.div
          className="photo-detail-modal"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={closePhotoModal}
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100vw',
            height: '100vh',
            background: 'rgba(0,0,0,0.9)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 2000
          }}
        >
          <motion.div
            className="photo-detail-content"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            onClick={(e) => e.stopPropagation()}
            style={{
              background: '#222',
              padding: '2rem',
              borderRadius: '12px',
              maxWidth: '90vw',
              maxHeight: '90vh',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center'
            }}
          >
            <img
              src={selectedPhoto.photo.cloudinaryUrl}
              alt={`Photo by ${selectedPhoto.photo.guestName}`}
              style={{
                maxWidth: '100%',
                maxHeight: '60vh',
                objectFit: 'contain',
                borderRadius: '8px',
                marginBottom: '1rem'
              }}
            />
            <div className="photo-details" style={{ textAlign: 'center', color: '#fff' }}>
              <h3 style={{ marginBottom: '0.5rem' }}>
                Photo by {selectedPhoto.photo.guestName || 'Anonymous'}
              </h3>
              <p style={{ color: '#ccc', fontSize: '0.9rem' }}>
                Position: Row {selectedPhoto.row + 1}, Column {selectedPhoto.col + 1}
              </p>
              <p style={{ color: '#ccc', fontSize: '0.9rem' }}>
                Uploaded: {new Date(selectedPhoto.photo.uploadedAt).toLocaleString()}
              </p>
              {selectedPhoto.photo.guestEmail && (
                <p style={{ color: '#ccc', fontSize: '0.9rem' }}>
                  Email: {selectedPhoto.photo.guestEmail}
                </p>
              )}
            </div>
            <button
              onClick={closePhotoModal}
              style={{
                marginTop: '1rem',
                padding: '0.75rem 1.5rem',
                background: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                cursor: 'pointer',
                fontSize: '1rem'
              }}
            >
              Close
            </button>
          </motion.div>
        </motion.div>
      )}

      {/* Connection Status Overlay */}
      {!connected && (
        <div className="loading-overlay">
          <div className="loading-content">
            <div className="loading-spinner"></div>
            <p>Connecting to mosaic wall...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default MosaicWall;
