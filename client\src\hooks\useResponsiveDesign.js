import { useState, useEffect, useCallback } from 'react';

/**
 * Breakpoints for responsive design
 */
const BREAKPOINTS = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1400
};

/**
 * Hook for responsive design utilities
 */
export const useResponsiveDesign = () => {
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight
  });

  const [orientation, setOrientation] = useState(
    window.innerWidth > window.innerHeight ? 'landscape' : 'portrait'
  );

  const [deviceType, setDeviceType] = useState('desktop');

  // Update window size and derived values
  const updateSize = useCallback(() => {
    const width = window.innerWidth;
    const height = window.innerHeight;

    setWindowSize({ width, height });
    setOrientation(width > height ? 'landscape' : 'portrait');

    // Determine device type based on width and user agent
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    const isTablet = isMobile && Math.min(width, height) >= 768;
    
    if (width < BREAKPOINTS.md) {
      setDeviceType(isMobile ? 'mobile' : 'small-desktop');
    } else if (width < BREAKPOINTS.lg) {
      setDeviceType(isTablet ? 'tablet' : 'desktop');
    } else {
      setDeviceType('desktop');
    }
  }, []);

  useEffect(() => {
    updateSize();

    const handleResize = () => {
      updateSize();
    };

    const handleOrientationChange = () => {
      // Delay to ensure dimensions are updated
      setTimeout(updateSize, 100);
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
    };
  }, [updateSize]);

  // Get current breakpoint
  const getCurrentBreakpoint = useCallback(() => {
    const width = windowSize.width;
    
    if (width >= BREAKPOINTS.xxl) return 'xxl';
    if (width >= BREAKPOINTS.xl) return 'xl';
    if (width >= BREAKPOINTS.lg) return 'lg';
    if (width >= BREAKPOINTS.md) return 'md';
    if (width >= BREAKPOINTS.sm) return 'sm';
    return 'xs';
  }, [windowSize.width]);

  // Check if current width matches breakpoint
  const isBreakpoint = useCallback((breakpoint) => {
    return getCurrentBreakpoint() === breakpoint;
  }, [getCurrentBreakpoint]);

  // Check if current width is at least the specified breakpoint
  const isBreakpointUp = useCallback((breakpoint) => {
    return windowSize.width >= BREAKPOINTS[breakpoint];
  }, [windowSize.width]);

  // Check if current width is below the specified breakpoint
  const isBreakpointDown = useCallback((breakpoint) => {
    return windowSize.width < BREAKPOINTS[breakpoint];
  }, [windowSize.width]);

  // Get responsive value based on breakpoints
  const getResponsiveValue = useCallback((values) => {
    const currentBreakpoint = getCurrentBreakpoint();
    const breakpointOrder = ['xxl', 'xl', 'lg', 'md', 'sm', 'xs'];
    
    // Find the best matching value
    for (const bp of breakpointOrder) {
      if (values[bp] !== undefined && isBreakpointUp(bp)) {
        return values[bp];
      }
    }
    
    // Fallback to default or first available value
    return values.default || values[Object.keys(values)[0]];
  }, [getCurrentBreakpoint, isBreakpointUp]);

  // Calculate optimal grid dimensions for mosaic
  const getOptimalGridSize = useCallback((totalPhotos, aspectRatio = 1) => {
    const { width, height } = windowSize;
    const availableWidth = width - 40; // Account for padding
    const availableHeight = height - 120; // Account for header/footer
    
    // Calculate based on device type
    let maxTileSize, minTileSize;
    
    switch (deviceType) {
      case 'mobile':
        maxTileSize = 120;
        minTileSize = 60;
        break;
      case 'tablet':
        maxTileSize = 150;
        minTileSize = 80;
        break;
      default:
        maxTileSize = 200;
        minTileSize = 100;
    }
    
    // Try different grid configurations
    const configurations = [];
    
    for (let cols = 1; cols <= 20; cols++) {
      const rows = Math.ceil(totalPhotos / cols);
      const tileWidth = availableWidth / cols;
      const tileHeight = availableHeight / rows;
      const tileSize = Math.min(tileWidth, tileHeight);
      
      if (tileSize >= minTileSize && tileSize <= maxTileSize) {
        configurations.push({
          rows,
          cols,
          tileSize,
          efficiency: (rows * cols - totalPhotos) / (rows * cols), // Lower is better
          aspectRatioMatch: Math.abs((cols / rows) - aspectRatio)
        });
      }
    }
    
    // Sort by efficiency and aspect ratio match
    configurations.sort((a, b) => {
      const efficiencyDiff = a.efficiency - b.efficiency;
      if (Math.abs(efficiencyDiff) < 0.1) {
        return a.aspectRatioMatch - b.aspectRatioMatch;
      }
      return efficiencyDiff;
    });
    
    return configurations[0] || { rows: 5, cols: 5, tileSize: 100 };
  }, [windowSize, deviceType]);

  // Get responsive font sizes
  const getResponsiveFontSize = useCallback((baseSize = 16) => {
    const scaleFactor = getResponsiveValue({
      xs: 0.8,
      sm: 0.9,
      md: 1,
      lg: 1.1,
      xl: 1.2,
      xxl: 1.3
    });
    
    return baseSize * scaleFactor;
  }, [getResponsiveValue]);

  // Get responsive spacing
  const getResponsiveSpacing = useCallback((baseSpacing = 16) => {
    const scaleFactor = getResponsiveValue({
      xs: 0.5,
      sm: 0.75,
      md: 1,
      lg: 1.25,
      xl: 1.5,
      xxl: 1.75
    });
    
    return baseSpacing * scaleFactor;
  }, [getResponsiveValue]);

  // Check if device supports touch
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

  // Check if device prefers reduced motion
  const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

  // Get safe area insets for mobile devices
  const getSafeAreaInsets = useCallback(() => {
    const style = getComputedStyle(document.documentElement);
    
    return {
      top: parseInt(style.getPropertyValue('env(safe-area-inset-top)') || '0'),
      right: parseInt(style.getPropertyValue('env(safe-area-inset-right)') || '0'),
      bottom: parseInt(style.getPropertyValue('env(safe-area-inset-bottom)') || '0'),
      left: parseInt(style.getPropertyValue('env(safe-area-inset-left)') || '0')
    };
  }, []);

  return {
    windowSize,
    orientation,
    deviceType,
    currentBreakpoint: getCurrentBreakpoint(),
    isBreakpoint,
    isBreakpointUp,
    isBreakpointDown,
    getResponsiveValue,
    getOptimalGridSize,
    getResponsiveFontSize,
    getResponsiveSpacing,
    isTouchDevice,
    prefersReducedMotion,
    getSafeAreaInsets,
    
    // Convenience flags
    isMobile: deviceType === 'mobile',
    isTablet: deviceType === 'tablet',
    isDesktop: deviceType === 'desktop',
    isSmallScreen: isBreakpointDown('md'),
    isLargeScreen: isBreakpointUp('lg'),
    isPortrait: orientation === 'portrait',
    isLandscape: orientation === 'landscape'
  };
};

/**
 * Hook for managing responsive images
 */
export const useResponsiveImages = () => {
  const { windowSize, deviceType, getCurrentBreakpoint } = useResponsiveDesign();

  // Get optimal image size based on display context
  const getOptimalImageSize = useCallback((context = 'grid') => {
    const devicePixelRatio = window.devicePixelRatio || 1;
    const breakpoint = getCurrentBreakpoint();
    
    let baseSize;
    
    switch (context) {
      case 'thumbnail':
        baseSize = { xs: 60, sm: 80, md: 100, lg: 120, xl: 150 };
        break;
      case 'grid':
        baseSize = { xs: 100, sm: 120, md: 150, lg: 180, xl: 200 };
        break;
      case 'detail':
        baseSize = { xs: 300, sm: 400, md: 500, lg: 600, xl: 800 };
        break;
      default:
        baseSize = { xs: 150, sm: 200, md: 250, lg: 300, xl: 400 };
    }
    
    const size = baseSize[breakpoint] || baseSize.md;
    
    // Account for device pixel ratio, but cap at 2x for performance
    const scaledSize = Math.round(size * Math.min(devicePixelRatio, 2));
    
    return {
      width: scaledSize,
      height: scaledSize,
      quality: deviceType === 'mobile' ? 'auto:low' : 'auto:good'
    };
  }, [deviceType, getCurrentBreakpoint]);

  return {
    getOptimalImageSize
  };
};
