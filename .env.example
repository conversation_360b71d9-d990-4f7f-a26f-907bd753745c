# MongoDB Configuration
MONGODB_URI=mongodb+srv://digambarkothawale05:<EMAIL>/mosaic

# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=dfhelz5am
CLOUDINARY_API_KEY=416172676617427
CLOUDINARY_API_SECRET=WHK7kw0YQottE3gxEeIGuaapbhA

# Server Configuration
PORT=5000
NODE_ENV=development

# Frontend Configuration
REACT_APP_API_URL=http://localhost:5000
REACT_APP_SOCKET_URL=http://localhost:5000

# Admin Configuration
ADMIN_PASSWORD=admin123
JWT_SECRET=your-jwt-secret-key-here

# Grid Configuration
DEFAULT_GRID_ROWS=10
DEFAULT_GRID_COLS=10
MAX_GRID_SIZE=50
