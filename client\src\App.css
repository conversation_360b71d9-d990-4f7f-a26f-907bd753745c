/* Global styles to prevent scrolling */
html, body {
  margin: 0;
  padding: 0;
  overflow: hidden;
  width: 100vw;
  height: 100vh;
  background: #000;
}

/* Allow scrolling for admin and capture pages */
body.admin-page,
body.capture-page {
  overflow: auto !important;
  height: auto !important;
}

.App {
  width: 100vw;
  height: 100vh;
  background: #000;
  color: #fff;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
}

/* Additional app-specific styles */
.page-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.error-message {
  background: rgba(220, 53, 69, 0.1);
  border: 1px solid #dc3545;
  color: #dc3545;
  padding: 1rem;
  border-radius: 6px;
  margin: 1rem 0;
  text-align: center;
}

.success-message {
  background: rgba(40, 167, 69, 0.1);
  border: 1px solid #28a745;
  color: #28a745;
  padding: 1rem;
  border-radius: 6px;
  margin: 1rem 0;
  text-align: center;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  text-align: center;
  color: #fff;
}

.loading-content .loading-spinner {
  width: 40px;
  height: 40px;
  margin-bottom: 1rem;
}
