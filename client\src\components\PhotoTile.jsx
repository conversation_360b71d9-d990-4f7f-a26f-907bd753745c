import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { useImageOptimization, useProgressiveImage } from '../hooks/useImageOptimization';

const PhotoTile = ({
  photo,
  row,
  col,
  size = 100,
  isNew = false,
  onClick,
  showGuestName = true,
  fullscreen = false,
  isAdmin = false
}) => {
  // Handle both square (number) and rectangular (object) tile sizes
  const tileWidth = typeof size === 'object' ? size.width : size;
  const tileHeight = typeof size === 'object' ? size.height : size;
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const imgRef = useRef(null);
  const { getOptimizedImageUrl, lazyLoadImage } = useImageOptimization();

  // Use direct Cloudinary URL for fastest loading
  const imageUrl = photo?.cloudinaryUrl || null;

  // Skip progressive loading for faster display
  const progressiveSrc = imageUrl;
  const progressiveLoading = false;

  useEffect(() => {
    if (imageUrl) {
      setImageLoaded(false);
      setImageError(false);
      // Load image immediately without lazy loading for faster display
    }
  }, [imageUrl]);

  const handleImageLoad = () => {
    setImageLoaded(true);
  };

  const handleImageError = () => {
    console.warn('Image failed to load:', imageUrl);
    setImageError(true);
    setImageLoaded(false);
  };

  const handleClick = () => {
    if (onClick && photo) {
      onClick(photo, row, col);
    }
  };

  // Animation variants
  const tileVariants = {
    hidden: { 
      scale: 0, 
      rotate: 180, 
      opacity: 0 
    },
    visible: { 
      scale: 1, 
      rotate: 0, 
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 260,
        damping: 20,
        duration: 0.8
      }
    },
    hover: {
      scale: 1.05,
      zIndex: 10,
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 25
      }
    }
  };

  const imageVariants = {
    hidden: { opacity: 0 },
    visible: { 
      opacity: 1,
      transition: { duration: 0.3 }
    }
  };

  // Determine tile content and styling
  const getTileContent = () => {
    if (!photo) {
      // Empty tile
      const emptyTileStyle = {
        width: tileWidth,
        height: tileHeight
      };

      return (
        <div
          className={`photo-tile empty ${fullscreen ? 'fullscreen' : ''}`}
          style={emptyTileStyle}
        >
          <div className="empty-tile-content">
            {!fullscreen && <span className="empty-tile-icon">📷</span>}
          </div>
        </div>
      );
    }

    if (imageError) {
      // Error state
      return (
        <div className={`photo-tile error ${fullscreen ? 'fullscreen' : ''}`} style={{ width: size, height: size }}>
          <div className="error-tile-content">
            <span className="error-tile-icon">❌</span>
            {!fullscreen && <span className="error-tile-text">Failed to load</span>}
          </div>
        </div>
      );
    }

    return (
      <div
        className={`photo-tile ${isNew ? 'new-photo' : ''} ${!imageLoaded ? 'loading' : ''} ${progressiveLoading ? 'progressive-loading' : ''} ${fullscreen ? 'fullscreen' : ''}`}
        style={{ width: tileWidth, height: tileHeight }}
        onClick={handleClick}
      >
        {/* Main image - optimized for speed */}
        <motion.img
          ref={imgRef}
          src={progressiveSrc}
          alt={`Photo by ${photo.guestName || 'Anonymous'}`}
          onLoad={handleImageLoad}
          onError={handleImageError}
          variants={imageVariants}
          initial="hidden"
          animate="visible"
          style={{
            width: '100%',
            height: '100%',
            objectFit: 'cover',
            display: 'block'
          }}
          loading="eager"
          decoding="async"
        />

        {/* Guest name overlay */}
        {showGuestName && photo.guestName && imageLoaded && (
          <motion.div 
            className="guest-name-overlay"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
          >
            {photo.guestName}
          </motion.div>
        )}

        {/* Upload timestamp */}
        {photo.uploadedAt && imageLoaded && (
          <div className="upload-timestamp">
            {new Date(photo.uploadedAt).toLocaleTimeString()}
          </div>
        )}
      </div>
    );
  };

  return (
    <motion.div
      variants={tileVariants}
      initial={isNew ? "hidden" : "visible"}
      animate="visible"
      whileHover="hover"
      className="photo-tile-wrapper"
      data-row={row}
      data-col={col}
    >
      {getTileContent()}
    </motion.div>
  );
};

export default PhotoTile;
